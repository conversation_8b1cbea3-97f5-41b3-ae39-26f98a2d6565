<template>
  <div class="toolbar">
    <!-- 画布尺寸控制 -->
    <div class="toolbar-group">
      <el-select
        v-model="selectedPreset"
        placeholder="选择尺寸"
        size="small"
        style="width: 160px"
        @change="handlePresetChange"
      >
        <el-option
          v-for="preset in canvasStore.sizePresets"
          :key="preset.name"
          :label="preset.name"
          :value="preset.name"
        />
        <el-option label="自定义" value="custom" />
      </el-select>
      
      <template v-if="selectedPreset === 'custom'">
        <el-input
          v-model.number="customWidth"
          placeholder="宽度"
          size="small"
          style="width: 80px"
          @change="handleCustomSizeChange"
        />
        <span>×</span>
        <el-input
          v-model.number="customHeight"
          placeholder="高度"
          size="small"
          style="width: 80px"
          @change="handleCustomSizeChange"
        />
      </template>
    </div>

    <!-- 缩放控制 -->
    <div class="toolbar-group">
      <el-button-group size="small">
        <el-button @click="handleZoom(0.5)">50%</el-button>
        <el-button @click="handleZoom(1)">100%</el-button>
        <el-button @click="handleZoom(1.5)">150%</el-button>
        <el-button @click="handleZoom(2)">200%</el-button>
      </el-button-group>
      
      <el-slider
        v-model="zoomValue"
        :min="10"
        :max="500"
        :step="10"
        style="width: 120px; margin: 0 12px"
        @change="handleZoomSlider"
      />
      
      <span class="zoom-display">{{ Math.round(canvasStore.zoom * 100) }}%</span>
    </div>

    <!-- 背景控制 -->
    <div class="toolbar-group">
      <el-color-picker
        v-model="canvasStore.background.color"
        size="small"
        @change="handleBackgroundColorChange"
      />
      
      <el-upload
        :show-file-list="false"
        :before-upload="handleBackgroundUpload"
        accept="image/*"
      >
        <el-button size="small" type="primary">
          <el-icon><Picture /></el-icon>
          背景图
        </el-button>
      </el-upload>
      
      <el-select
        v-model="canvasStore.background.mode"
        size="small"
        style="width: 100px"
        @change="handleBackgroundModeChange"
      >
        <el-option label="原始" value="original" />
        <el-option label="撑满" value="stretch" />
        <el-option label="完整" value="fit" />
        <el-option label="重复" value="repeat" />
        <el-option label="水平重复" value="repeat-x" />
        <el-option label="垂直重复" value="repeat-y" />
        <el-option label="不重复" value="no-repeat" />
        <el-option label="覆盖" value="cover" />
        <el-option label="包含" value="contain" />
      </el-select>
    </div>

    <!-- 辅助工具 -->
    <div class="toolbar-group">
      <el-button
        size="small"
        :type="canvasStore.gridEnabled ? 'primary' : 'default'"
        @click="canvasStore.toggleGrid()"
      >
        <el-icon><Grid /></el-icon>
        网格
      </el-button>
      
      <el-button
        size="small"
        :type="canvasStore.showGuidelines ? 'primary' : 'default'"
        @click="canvasStore.toggleGuidelines()"
      >
        <el-icon><Aim /></el-icon>
        参考线
      </el-button>
      
      <el-button
        size="small"
        :type="canvasStore.snapToGrid ? 'primary' : 'default'"
        @click="canvasStore.toggleSnapToGrid()"
      >
        <el-icon><Magnet /></el-icon>
        吸附
      </el-button>
    </div>

    <!-- 操作按钮 -->
    <div class="toolbar-group">
      <el-button size="small" @click="handleUndo">
        <el-icon><Back /></el-icon>
        撤销
      </el-button>

      <el-button size="small" @click="handleRedo">
        <el-icon><Right /></el-icon>
        重做
      </el-button>
    </div>

    <!-- 坐标显示 -->
    <div class="toolbar-group">
      <span class="coordinate-display">
        X: {{ Math.round(canvasStore.position.x) }}, 
        Y: {{ Math.round(canvasStore.position.y) }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import {
  Picture,
  Grid,
  Aim,
  Magnet,
  Back,
  Right
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const canvasStore = useCanvasStore()

// 选中的预设尺寸
const selectedPreset = ref('PC - 1920×1080')
const customWidth = ref(1920)
const customHeight = ref(1080)

// 缩放值（用于滑块）
const zoomValue = computed({
  get: () => Math.round(canvasStore.zoom * 100),
  set: (value: number) => {
    canvasStore.setZoom(value / 100)
  }
})

// 监听画布尺寸变化，更新自定义尺寸输入框
watch(() => canvasStore.size, (newSize) => {
  customWidth.value = newSize.width
  customHeight.value = newSize.height
}, { deep: true })

// 处理预设尺寸变化
const handlePresetChange = (presetName: string) => {
  if (presetName === 'custom') {
    return
  }
  
  const preset = canvasStore.sizePresets.find(p => p.name === presetName)
  if (preset) {
    canvasStore.setCanvasSize({ width: preset.width, height: preset.height })
  }
}

// 处理自定义尺寸变化
const handleCustomSizeChange = () => {
  if (customWidth.value > 0 && customHeight.value > 0) {
    canvasStore.setCanvasSize({ 
      width: customWidth.value, 
      height: customHeight.value 
    })
  }
}

// 处理缩放
const handleZoom = (zoom: number) => {
  canvasStore.setZoom(zoom)
}

// 处理缩放滑块
const handleZoomSlider = (value: number) => {
  canvasStore.setZoom(value / 100)
}

// 处理背景颜色变化
const handleBackgroundColorChange = (color: string) => {
  canvasStore.setBackgroundColor(color)
}

// 处理背景图上传
const handleBackgroundUpload = (file: File) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    const result = e.target?.result as string
    canvasStore.setBackgroundImage(result)
    ElMessage.success('背景图上传成功')
  }
  reader.readAsDataURL(file)
  return false // 阻止默认上传行为
}

// 处理背景模式变化
const handleBackgroundModeChange = (mode: string) => {
  // 模式已经通过v-model自动更新到store
}

// 撤销操作
const handleUndo = () => {
  // TODO: 实现撤销逻辑
  ElMessage.info('撤销功能待实现')
}

// 重做操作
const handleRedo = () => {
  // TODO: 实现重做逻辑
  ElMessage.info('重做功能待实现')
}
</script>

<style scoped>
.toolbar {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 0 16px;
  height: 50px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  overflow-x: auto;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 12px;
  border-right: 1px solid #f0f0f0;
  white-space: nowrap;
}

.toolbar-group:last-child {
  border-right: none;
}

.zoom-display {
  font-size: 12px;
  color: #606266;
  min-width: 40px;
  text-align: center;
}

.coordinate-display {
  font-size: 12px;
  color: #606266;
  font-family: monospace;
}

:deep(.el-slider__runway) {
  height: 4px;
}

:deep(.el-slider__button) {
  width: 12px;
  height: 12px;
}
</style>
