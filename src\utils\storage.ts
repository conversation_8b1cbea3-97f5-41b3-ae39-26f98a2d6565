// 状态持久化工具类

export interface StorageOptions {
  prefix?: string
  storage?: Storage
  serializer?: {
    serialize: (value: any) => string
    deserialize: (value: string) => any
  }
}

export class PersistentStorage {
  private prefix: string
  private storage: Storage
  private serializer: {
    serialize: (value: any) => string
    deserialize: (value: string) => any
  }

  constructor(options: StorageOptions = {}) {
    this.prefix = options.prefix || 'visual-editor:'
    this.storage = options.storage || localStorage
    this.serializer = options.serializer || {
      serialize: JSON.stringify,
      deserialize: JSON.parse
    }
  }

  // 生成完整的键名
  private getKey(key: string): string {
    return this.prefix + key
  }

  // 设置值
  set<T>(key: string, value: T): boolean {
    try {
      const serializedValue = this.serializer.serialize(value)
      this.storage.setItem(this.getKey(key), serializedValue)
      return true
    } catch (error) {
      console.error('Failed to set storage item:', error)
      return false
    }
  }

  // 获取值
  get<T>(key: string, defaultValue?: T): T | undefined {
    try {
      const item = this.storage.getItem(this.getKey(key))
      if (item === null) {
        return defaultValue
      }
      return this.serializer.deserialize(item)
    } catch (error) {
      console.error('Failed to get storage item:', error)
      return defaultValue
    }
  }

  // 删除值
  remove(key: string): boolean {
    try {
      this.storage.removeItem(this.getKey(key))
      return true
    } catch (error) {
      console.error('Failed to remove storage item:', error)
      return false
    }
  }

  // 清除所有带前缀的项
  clear(): boolean {
    try {
      const keys = Object.keys(this.storage).filter(key => 
        key.startsWith(this.prefix)
      )
      keys.forEach(key => this.storage.removeItem(key))
      return true
    } catch (error) {
      console.error('Failed to clear storage:', error)
      return false
    }
  }

  // 检查键是否存在
  has(key: string): boolean {
    return this.storage.getItem(this.getKey(key)) !== null
  }

  // 获取所有键
  keys(): string[] {
    return Object.keys(this.storage)
      .filter(key => key.startsWith(this.prefix))
      .map(key => key.substring(this.prefix.length))
  }

  // 获取存储大小（字节）
  size(): number {
    let total = 0
    this.keys().forEach(key => {
      const item = this.storage.getItem(this.getKey(key))
      if (item) {
        total += item.length
      }
    })
    return total
  }
}

// 项目状态接口
export interface ProjectState {
  canvas: {
    size: { width: number, height: number }
    zoom: number
    position: { x: number, y: number }
    background: {
      color: string
      image: string | null
      mode: string
    }
    gridEnabled: boolean
    gridSize: number
    snapToGrid: boolean
    showGuidelines: boolean
    guidelines: { x: number[], y: number[] }
  }
  elements: any[]
  events: {
    elementEvents: any[]
    globalEvents: any[]
  }
  history: any[]
  settings: {
    autoSave: boolean
    autoSaveInterval: number
    theme: string
    language: string
  }
}

// 项目管理器
export class ProjectManager {
  private storage: PersistentStorage
  private autoSaveTimer: number | null = null

  constructor() {
    this.storage = new PersistentStorage({
      prefix: 'visual-editor:project:'
    })
  }

  // 保存项目
  saveProject(name: string, state: ProjectState): boolean {
    const project = {
      name,
      state,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      version: '1.0.0'
    }

    return this.storage.set(name, project)
  }

  // 加载项目
  loadProject(name: string): ProjectState | null {
    const project = this.storage.get(name)
    if (project && project.state) {
      return project.state
    }
    return null
  }

  // 删除项目
  deleteProject(name: string): boolean {
    return this.storage.remove(name)
  }

  // 获取所有项目列表
  getProjectList(): Array<{
    name: string
    createdAt: number
    updatedAt: number
    version: string
  }> {
    const projects: any[] = []
    
    this.storage.keys().forEach(key => {
      const project = this.storage.get(key)
      if (project) {
        projects.push({
          name: project.name,
          createdAt: project.createdAt,
          updatedAt: project.updatedAt,
          version: project.version
        })
      }
    })

    return projects.sort((a, b) => b.updatedAt - a.updatedAt)
  }

  // 导出项目
  exportProject(name: string): string | null {
    const project = this.storage.get(name)
    if (project) {
      return JSON.stringify(project, null, 2)
    }
    return null
  }

  // 导入项目
  importProject(data: string): boolean {
    try {
      const project = JSON.parse(data)
      if (project.name && project.state) {
        return this.saveProject(project.name, project.state)
      }
      return false
    } catch (error) {
      console.error('Failed to import project:', error)
      return false
    }
  }

  // 启用自动保存
  enableAutoSave(name: string, getState: () => ProjectState, interval: number = 30000) {
    this.disableAutoSave()
    
    this.autoSaveTimer = window.setInterval(() => {
      const state = getState()
      this.saveProject(name, state)
      console.log('Project auto-saved:', name)
    }, interval)
  }

  // 禁用自动保存
  disableAutoSave() {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer)
      this.autoSaveTimer = null
    }
  }

  // 获取存储使用情况
  getStorageInfo() {
    const totalSize = this.storage.size()
    const projectCount = this.storage.keys().length
    
    return {
      totalSize,
      projectCount,
      formattedSize: this.formatBytes(totalSize)
    }
  }

  // 格式化字节数
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}

// 设置管理器
export class SettingsManager {
  private storage: PersistentStorage

  constructor() {
    this.storage = new PersistentStorage({
      prefix: 'visual-editor:settings:'
    })
  }

  // 获取设置
  get<T>(key: string, defaultValue?: T): T | undefined {
    return this.storage.get(key, defaultValue)
  }

  // 设置值
  set<T>(key: string, value: T): boolean {
    return this.storage.set(key, value)
  }

  // 获取所有设置
  getAll(): Record<string, any> {
    const settings: Record<string, any> = {}
    
    this.storage.keys().forEach(key => {
      settings[key] = this.storage.get(key)
    })
    
    return settings
  }

  // 重置所有设置
  reset(): boolean {
    return this.storage.clear()
  }

  // 导出设置
  export(): string {
    return JSON.stringify(this.getAll(), null, 2)
  }

  // 导入设置
  import(data: string): boolean {
    try {
      const settings = JSON.parse(data)
      Object.entries(settings).forEach(([key, value]) => {
        this.set(key, value)
      })
      return true
    } catch (error) {
      console.error('Failed to import settings:', error)
      return false
    }
  }
}

// 创建全局实例
export const projectManager = new ProjectManager()
export const settingsManager = new SettingsManager()
export const storage = new PersistentStorage()
