# 使用指南

## 🚀 快速开始

### 1. 启动项目
```bash
npm run dev
```
项目将在 http://localhost:3000 启动

### 2. 界面介绍

#### 主界面布局
- **顶部菜单栏**: 项目管理、性能监控
- **左侧组件库**: 可拖拽的组件面板
- **中央画布**: 主要编辑区域
- **右侧属性面板**: 元素属性和事件配置
- **工具栏**: 画布控制和操作工具

## 📝 基本操作

### 画布操作
1. **调整画布尺寸**
   - 在工具栏选择预设尺寸
   - 或选择"自定义"输入具体尺寸

2. **缩放画布**
   - 使用工具栏的缩放按钮（50%、100%、150%、200%）
   - 或拖动缩放滑块

3. **设置背景**
   - 点击颜色选择器设置背景色
   - 点击"背景图"按钮上传背景图片
   - 选择背景显示模式

### 添加组件
1. **从组件库拖拽**
   - 在左侧组件库中找到需要的组件
   - 拖拽到画布中央区域
   - 松开鼠标完成添加

2. **组件分类**
   - **电气符号**: 开关、电阻、电容等
   - **DataV边框**: 各种装饰性边框
   - **DataV装饰**: 装饰线条、圆点等
   - **基础图形**: 矩形、圆形、三角形

### 编辑元素
1. **选择元素**
   - 单击选择单个元素
   - Ctrl+点击多选元素
   - Ctrl+A 全选

2. **移动元素**
   - 拖拽选中的元素
   - 使用方向键微调位置

3. **修改属性**
   - 选中元素后在右侧属性面板修改
   - 支持位置、尺寸、颜色、透明度等

## ⌨️ 快捷键

| 快捷键 | 功能 |
|--------|------|
| Ctrl+A | 全选 |
| Ctrl+C | 复制 |
| Ctrl+V | 粘贴 |
| Ctrl+X | 剪切 |
| Ctrl+Z | 撤销 |
| Ctrl+Y | 重做 |
| Ctrl+G | 组合 |
| Ctrl+U | 取消组合 |
| Delete | 删除选中元素 |
| Ctrl+S | 保存项目 |

## 🎨 高级功能

### 组合操作
1. 选择多个元素
2. 按 Ctrl+G 或点击属性面板的"组合"按钮
3. 组合后的元素可以作为整体操作

### 层级控制
- **置顶**: 将元素移到最上层
- **上移**: 向上移动一层
- **下移**: 向下移动一层
- **置底**: 将元素移到最下层

### 对齐功能
1. 选择多个元素
2. 在属性面板选择对齐方式：
   - 左对齐
   - 居中对齐
   - 右对齐

### 辅助工具
1. **网格**
   - 点击工具栏"网格"按钮开启/关闭
   - 帮助精确定位元素

2. **参考线**
   - 点击工具栏"参考线"按钮开启/关闭
   - 拖拽时显示对齐参考线

3. **吸附**
   - 点击工具栏"吸附"按钮开启/关闭
   - 元素自动吸附到网格和参考线

## 🎭 事件配置

### 添加事件
1. 选择元素
2. 在属性面板切换到"事件配置"
3. 点击"添加事件"
4. 选择事件类型（点击、鼠标悬停等）
5. 添加动作（改变属性、播放动画等）

### 事件类型
- **click**: 鼠标点击
- **mouseover**: 鼠标悬停
- **mouseout**: 鼠标离开
- **dblclick**: 双击

### 动作类型
- **changeProp**: 改变元素属性
- **playAnimation**: 播放动画效果
- **navigate**: 页面跳转
- **toggleVisible**: 显示/隐藏元素

## 💾 项目管理

### 创建项目
1. 点击顶部菜单"项目管理"
2. 点击"新建项目"
3. 输入项目名称
4. 选择画布尺寸预设
5. 点击"创建"

### 保存和加载
- **自动保存**: 项目会自动保存到浏览器本地存储
- **手动保存**: 使用 Ctrl+S 快捷键
- **加载项目**: 在项目管理中选择已有项目

### 导入导出
- **导出**: 在项目管理中点击"导出"下载 JSON 文件
- **导入**: 点击"导入项目"选择 JSON 文件

## 📊 性能监控

点击顶部菜单"性能监控"查看：
- **帧率**: 当前渲染帧率
- **渲染时间**: 每帧渲染耗时
- **内存使用**: JavaScript 内存占用
- **元素数量**: 画布中的元素总数
- **性能等级**: 综合性能评估

## 🔧 常见问题

### Q: 组件拖拽没有反应？
A: 确保拖拽到画布的白色区域，不要拖拽到工具栏或面板上。

### Q: 快捷键不生效？
A: 确保焦点在画布区域，不要在输入框中使用快捷键。

### Q: 项目保存失败？
A: 检查浏览器是否允许本地存储，或尝试清除浏览器缓存。

### Q: 性能较差怎么办？
A: 
- 减少画布中的元素数量
- 关闭不必要的辅助工具（网格、参考线）
- 在性能监控面板查看具体指标

## 💡 使用技巧

1. **批量操作**: 使用 Ctrl+点击选择多个元素进行批量编辑
2. **精确定位**: 开启网格和吸附功能，便于精确对齐
3. **快速复制**: 使用 Ctrl+D 快速复制选中元素
4. **预览效果**: 配置事件后点击元素预览交互效果
5. **性能优化**: 定期查看性能监控，及时优化项目

## 🎯 最佳实践

1. **项目规划**: 开始前规划好画布尺寸和整体布局
2. **分层管理**: 合理使用组合功能管理复杂图形
3. **命名规范**: 为重要元素设置有意义的名称
4. **定期保存**: 重要修改后及时保存项目
5. **性能监控**: 复杂项目要关注性能指标
