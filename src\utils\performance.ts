// 性能优化工具类

export interface PerformanceMetrics {
  renderTime: number
  frameRate: number
  memoryUsage: number
  elementCount: number
  lastUpdate: number
}

export class PerformanceMonitor {
  private metrics: PerformanceMetrics = {
    renderTime: 0,
    frameRate: 0,
    memoryUsage: 0,
    elementCount: 0,
    lastUpdate: Date.now()
  }
  
  private frameCount = 0
  private lastFrameTime = performance.now()
  private renderTimes: number[] = []
  private maxSamples = 60

  // 开始渲染计时
  startRender(): number {
    return performance.now()
  }

  // 结束渲染计时
  endRender(startTime: number, elementCount: number = 0) {
    const renderTime = performance.now() - startTime
    this.renderTimes.push(renderTime)
    
    if (this.renderTimes.length > this.maxSamples) {
      this.renderTimes.shift()
    }

    this.metrics.renderTime = this.getAverageRenderTime()
    this.metrics.elementCount = elementCount
    this.updateFrameRate()
    this.updateMemoryUsage()
    this.metrics.lastUpdate = Date.now()
  }

  // 更新帧率
  private updateFrameRate() {
    const now = performance.now()
    const deltaTime = now - this.lastFrameTime
    
    if (deltaTime >= 1000) {
      this.metrics.frameRate = Math.round((this.frameCount * 1000) / deltaTime)
      this.frameCount = 0
      this.lastFrameTime = now
    } else {
      this.frameCount++
    }
  }

  // 更新内存使用情况
  private updateMemoryUsage() {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      this.metrics.memoryUsage = memory.usedJSHeapSize / 1024 / 1024 // MB
    }
  }

  // 获取平均渲染时间
  private getAverageRenderTime(): number {
    if (this.renderTimes.length === 0) return 0
    const sum = this.renderTimes.reduce((a, b) => a + b, 0)
    return sum / this.renderTimes.length
  }

  // 获取性能指标
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics }
  }

  // 重置指标
  reset() {
    this.renderTimes = []
    this.frameCount = 0
    this.lastFrameTime = performance.now()
    this.metrics = {
      renderTime: 0,
      frameRate: 0,
      memoryUsage: 0,
      elementCount: 0,
      lastUpdate: Date.now()
    }
  }

  // 获取性能等级
  getPerformanceLevel(): 'excellent' | 'good' | 'fair' | 'poor' {
    const { renderTime, frameRate } = this.metrics
    
    if (frameRate >= 55 && renderTime < 5) return 'excellent'
    if (frameRate >= 45 && renderTime < 10) return 'good'
    if (frameRate >= 30 && renderTime < 20) return 'fair'
    return 'poor'
  }
}

// 虚拟滚动实现
export class VirtualScroller {
  private container: HTMLElement
  private itemHeight: number
  private visibleCount: number
  private startIndex = 0
  private endIndex = 0
  private scrollTop = 0

  constructor(container: HTMLElement, itemHeight: number) {
    this.container = container
    this.itemHeight = itemHeight
    this.visibleCount = Math.ceil(container.clientHeight / itemHeight) + 2 // 缓冲区
    
    this.container.addEventListener('scroll', this.handleScroll.bind(this))
  }

  private handleScroll() {
    this.scrollTop = this.container.scrollTop
    this.updateVisibleRange()
  }

  private updateVisibleRange() {
    this.startIndex = Math.floor(this.scrollTop / this.itemHeight)
    this.endIndex = Math.min(this.startIndex + this.visibleCount, this.getTotalCount())
  }

  // 获取可见范围
  getVisibleRange(): { start: number, end: number } {
    return { start: this.startIndex, end: this.endIndex }
  }

  // 设置总数量（需要子类实现）
  protected getTotalCount(): number {
    return 0
  }

  // 滚动到指定项
  scrollToItem(index: number) {
    const scrollTop = index * this.itemHeight
    this.container.scrollTop = scrollTop
  }

  // 销毁
  destroy() {
    this.container.removeEventListener('scroll', this.handleScroll.bind(this))
  }
}

// 增量渲染器
export class IncrementalRenderer {
  private renderQueue: Array<() => void> = []
  private isRendering = false
  private maxRenderTime = 16 // 16ms per frame (60fps)
  private performanceMonitor: PerformanceMonitor

  constructor(performanceMonitor: PerformanceMonitor) {
    this.performanceMonitor = performanceMonitor
  }

  // 添加渲染任务
  addRenderTask(task: () => void) {
    this.renderQueue.push(task)
    this.scheduleRender()
  }

  // 批量添加渲染任务
  addRenderTasks(tasks: Array<() => void>) {
    this.renderQueue.push(...tasks)
    this.scheduleRender()
  }

  // 调度渲染
  private scheduleRender() {
    if (!this.isRendering) {
      this.isRendering = true
      requestAnimationFrame(this.render.bind(this))
    }
  }

  // 执行渲染
  private render() {
    const startTime = this.performanceMonitor.startRender()
    const frameStartTime = performance.now()

    while (this.renderQueue.length > 0 && (performance.now() - frameStartTime) < this.maxRenderTime) {
      const task = this.renderQueue.shift()
      if (task) {
        task()
      }
    }

    this.performanceMonitor.endRender(startTime)

    if (this.renderQueue.length > 0) {
      // 还有任务，继续下一帧
      requestAnimationFrame(this.render.bind(this))
    } else {
      this.isRendering = false
    }
  }

  // 清空渲染队列
  clear() {
    this.renderQueue = []
    this.isRendering = false
  }

  // 获取队列长度
  getQueueLength(): number {
    return this.renderQueue.length
  }
}

// 对象池
export class ObjectPool<T> {
  private pool: T[] = []
  private createFn: () => T
  private resetFn?: (obj: T) => void
  private maxSize: number

  constructor(createFn: () => T, resetFn?: (obj: T) => void, maxSize: number = 100) {
    this.createFn = createFn
    this.resetFn = resetFn
    this.maxSize = maxSize
  }

  // 获取对象
  get(): T {
    if (this.pool.length > 0) {
      return this.pool.pop()!
    }
    return this.createFn()
  }

  // 释放对象
  release(obj: T) {
    if (this.pool.length < this.maxSize) {
      if (this.resetFn) {
        this.resetFn(obj)
      }
      this.pool.push(obj)
    }
  }

  // 预填充池
  preFill(count: number) {
    for (let i = 0; i < count; i++) {
      if (this.pool.length < this.maxSize) {
        this.pool.push(this.createFn())
      }
    }
  }

  // 清空池
  clear() {
    this.pool = []
  }

  // 获取池大小
  size(): number {
    return this.pool.length
  }
}

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate?: boolean
): (...args: Parameters<T>) => void {
  let timeout: number | null = null
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }
    
    const callNow = immediate && !timeout
    
    if (timeout) clearTimeout(timeout)
    timeout = window.setTimeout(later, wait)
    
    if (callNow) func(...args)
  }
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// Web Worker 管理器
export class WorkerManager {
  private workers: Map<string, Worker> = new Map()
  private taskQueue: Map<string, Array<{ data: any, resolve: Function, reject: Function }>> = new Map()

  // 创建 Worker
  createWorker(name: string, scriptUrl: string): Worker {
    const worker = new Worker(scriptUrl)
    this.workers.set(name, worker)
    this.taskQueue.set(name, [])
    
    worker.onmessage = (event) => {
      const queue = this.taskQueue.get(name)
      if (queue && queue.length > 0) {
        const task = queue.shift()
        if (task) {
          task.resolve(event.data)
        }
      }
    }
    
    worker.onerror = (error) => {
      const queue = this.taskQueue.get(name)
      if (queue && queue.length > 0) {
        const task = queue.shift()
        if (task) {
          task.reject(error)
        }
      }
    }
    
    return worker
  }

  // 执行任务
  executeTask(workerName: string, data: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const worker = this.workers.get(workerName)
      const queue = this.taskQueue.get(workerName)
      
      if (!worker || !queue) {
        reject(new Error(`Worker ${workerName} not found`))
        return
      }
      
      queue.push({ data, resolve, reject })
      worker.postMessage(data)
    })
  }

  // 销毁 Worker
  destroyWorker(name: string) {
    const worker = this.workers.get(name)
    if (worker) {
      worker.terminate()
      this.workers.delete(name)
      this.taskQueue.delete(name)
    }
  }

  // 销毁所有 Worker
  destroyAll() {
    this.workers.forEach((worker, name) => {
      this.destroyWorker(name)
    })
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor()
export const workerManager = new WorkerManager()
