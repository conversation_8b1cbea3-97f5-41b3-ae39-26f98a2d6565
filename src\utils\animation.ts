// GSAP 动画工具类
import { gsap } from 'gsap'
import type { Meta2dElement } from './meta2d'

export interface AnimationConfig {
  duration?: number
  delay?: number
  ease?: string
  repeat?: number
  yoyo?: boolean
  onComplete?: () => void
  onUpdate?: () => void
}

export interface AnimationPreset {
  name: string
  label: string
  config: (element: Meta2dElement) => any
  duration: number
}

export class AnimationManager {
  private activeAnimations: gsap.core.Tween[] = []

  // 基础动画方法
  animateElement(element: Meta2dElement, props: any, config: AnimationConfig = {}) {
    const tween = gsap.to(element, {
      ...props,
      duration: config.duration || 1,
      delay: config.delay || 0,
      ease: config.ease || 'power2.out',
      repeat: config.repeat || 0,
      yoyo: config.yoyo || false,
      onComplete: config.onComplete,
      onUpdate: config.onUpdate
    })

    this.activeAnimations.push(tween)
    return tween
  }

  // 淡入动画
  fadeIn(element: Meta2dElement, config: AnimationConfig = {}) {
    element.opacity = 0
    return this.animateElement(element, { opacity: 1 }, config)
  }

  // 淡出动画
  fadeOut(element: Meta2dElement, config: AnimationConfig = {}) {
    return this.animateElement(element, { opacity: 0 }, config)
  }

  // 缩放动画
  scale(element: Meta2dElement, scaleX: number, scaleY: number = scaleX, config: AnimationConfig = {}) {
    const originalWidth = element.width
    const originalHeight = element.height
    
    return this.animateElement(element, {
      width: originalWidth * scaleX,
      height: originalHeight * scaleY
    }, config)
  }

  // 移动动画
  moveTo(element: Meta2dElement, x: number, y: number, config: AnimationConfig = {}) {
    return this.animateElement(element, { x, y }, config)
  }

  // 旋转动画
  rotate(element: Meta2dElement, rotation: number, config: AnimationConfig = {}) {
    return this.animateElement(element, { rotate: rotation }, config)
  }

  // 弹跳动画
  bounce(element: Meta2dElement, config: AnimationConfig = {}) {
    const originalY = element.y
    return this.animateElement(element, {
      y: originalY - 20,
      ease: 'bounce.out'
    }, {
      ...config,
      duration: config.duration || 0.6
    })
  }

  // 摇摆动画
  shake(element: Meta2dElement, config: AnimationConfig = {}) {
    const originalX = element.x
    return this.animateElement(element, {
      x: originalX + 10,
      repeat: 5,
      yoyo: true,
      ease: 'power2.inOut'
    }, {
      ...config,
      duration: config.duration || 0.1
    })
  }

  // 滑入动画
  slideIn(element: Meta2dElement, direction: 'left' | 'right' | 'up' | 'down', config: AnimationConfig = {}) {
    const originalX = element.x
    const originalY = element.y
    
    let startX = originalX
    let startY = originalY
    
    switch (direction) {
      case 'left':
        startX = originalX - 100
        break
      case 'right':
        startX = originalX + 100
        break
      case 'up':
        startY = originalY - 100
        break
      case 'down':
        startY = originalY + 100
        break
    }
    
    element.x = startX
    element.y = startY
    
    return this.animateElement(element, {
      x: originalX,
      y: originalY
    }, config)
  }

  // 脉冲动画
  pulse(element: Meta2dElement, config: AnimationConfig = {}) {
    const originalWidth = element.width
    const originalHeight = element.height
    
    return this.animateElement(element, {
      width: originalWidth * 1.1,
      height: originalHeight * 1.1,
      repeat: -1,
      yoyo: true,
      ease: 'power2.inOut'
    }, {
      ...config,
      duration: config.duration || 1
    })
  }

  // 停止所有动画
  stopAll() {
    this.activeAnimations.forEach(tween => tween.kill())
    this.activeAnimations = []
  }

  // 停止特定元素的动画
  stopElement(element: Meta2dElement) {
    gsap.killTweensOf(element)
    this.activeAnimations = this.activeAnimations.filter(tween => 
      tween.targets().indexOf(element) === -1
    )
  }

  // 暂停所有动画
  pauseAll() {
    this.activeAnimations.forEach(tween => tween.pause())
  }

  // 恢复所有动画
  resumeAll() {
    this.activeAnimations.forEach(tween => tween.resume())
  }

  // 获取动画预设
  getPresets(): AnimationPreset[] {
    return [
      {
        name: 'fadeIn',
        label: '淡入',
        duration: 1,
        config: (element) => ({ opacity: 1 })
      },
      {
        name: 'fadeOut',
        label: '淡出',
        duration: 1,
        config: (element) => ({ opacity: 0 })
      },
      {
        name: 'scaleUp',
        label: '放大',
        duration: 0.5,
        config: (element) => ({ 
          width: element.width * 1.2, 
          height: element.height * 1.2 
        })
      },
      {
        name: 'scaleDown',
        label: '缩小',
        duration: 0.5,
        config: (element) => ({ 
          width: element.width * 0.8, 
          height: element.height * 0.8 
        })
      },
      {
        name: 'slideLeft',
        label: '左移',
        duration: 0.5,
        config: (element) => ({ x: element.x - 50 })
      },
      {
        name: 'slideRight',
        label: '右移',
        duration: 0.5,
        config: (element) => ({ x: element.x + 50 })
      },
      {
        name: 'slideUp',
        label: '上移',
        duration: 0.5,
        config: (element) => ({ y: element.y - 50 })
      },
      {
        name: 'slideDown',
        label: '下移',
        duration: 0.5,
        config: (element) => ({ y: element.y + 50 })
      },
      {
        name: 'rotate',
        label: '旋转',
        duration: 1,
        config: (element) => ({ rotate: (element.rotate || 0) + 360 })
      },
      {
        name: 'bounce',
        label: '弹跳',
        duration: 0.6,
        config: (element) => ({ 
          y: element.y - 20,
          ease: 'bounce.out'
        })
      },
      {
        name: 'shake',
        label: '摇摆',
        duration: 0.5,
        config: (element) => ({
          x: element.x + 10,
          repeat: 5,
          yoyo: true,
          ease: 'power2.inOut'
        })
      }
    ]
  }

  // 执行预设动画
  playPreset(element: Meta2dElement, presetName: string, config: AnimationConfig = {}) {
    const preset = this.getPresets().find(p => p.name === presetName)
    if (!preset) {
      console.warn(`Animation preset "${presetName}" not found`)
      return null
    }

    const animationProps = preset.config(element)
    return this.animateElement(element, animationProps, {
      duration: preset.duration,
      ...config
    })
  }
}

// 创建全局动画管理器实例
export const animationManager = new AnimationManager()

// 缓动函数预设
export const easingPresets = [
  { label: '线性', value: 'none' },
  { label: '缓入', value: 'power2.in' },
  { label: '缓出', value: 'power2.out' },
  { label: '缓入缓出', value: 'power2.inOut' },
  { label: '弹性缓出', value: 'elastic.out' },
  { label: '弹性缓入', value: 'elastic.in' },
  { label: '弹性缓入缓出', value: 'elastic.inOut' },
  { label: '回弹缓出', value: 'back.out' },
  { label: '回弹缓入', value: 'back.in' },
  { label: '回弹缓入缓出', value: 'back.inOut' },
  { label: '弹跳缓出', value: 'bounce.out' },
  { label: '弹跳缓入', value: 'bounce.in' },
  { label: '弹跳缓入缓出', value: 'bounce.inOut' }
]
