<template>
  <div class="properties-panel">
    <div class="panel-header">
      <h3>属性面板</h3>
    </div>
    
    <div class="panel-content">
      <!-- 无选中元素时的提示 -->
      <div v-if="selectedElements.length === 0" class="empty-state">
        <el-icon class="empty-icon"><Select /></el-icon>
        <p>请选择画布中的元素</p>
      </div>
      
      <!-- 单个元素属性 -->
      <div v-else-if="selectedElements.length === 1" class="single-element">
        <div class="element-info">
          <h4>{{ selectedElements[0].name || '未命名元素' }}</h4>
          <p class="element-type">类型: {{ getElementTypeName(selectedElements[0]) }}</p>
        </div>
        
        <!-- 基础属性 -->
        <el-collapse v-model="activeCollapse" accordion>
          <el-collapse-item title="位置和尺寸" name="transform">
            <div class="property-group">
              <div class="property-row">
                <label>X坐标:</label>
                <el-input-number
                  v-model="elementProps.x"
                  size="small"
                  :step="1"
                  @change="updateElementProperty('x', $event)"
                />
              </div>
              
              <div class="property-row">
                <label>Y坐标:</label>
                <el-input-number
                  v-model="elementProps.y"
                  size="small"
                  :step="1"
                  @change="updateElementProperty('y', $event)"
                />
              </div>
              
              <div class="property-row">
                <label>宽度:</label>
                <el-input-number
                  v-model="elementProps.width"
                  size="small"
                  :min="1"
                  :step="1"
                  @change="updateElementProperty('width', $event)"
                />
              </div>
              
              <div class="property-row">
                <label>高度:</label>
                <el-input-number
                  v-model="elementProps.height"
                  size="small"
                  :min="1"
                  :step="1"
                  @change="updateElementProperty('height', $event)"
                />
              </div>
              
              <div class="property-row">
                <label>旋转:</label>
                <el-input-number
                  v-model="elementProps.rotate"
                  size="small"
                  :step="1"
                  :min="-360"
                  :max="360"
                  @change="updateElementProperty('rotate', $event)"
                />
              </div>
            </div>
          </el-collapse-item>
          
          <el-collapse-item title="外观样式" name="appearance">
            <div class="property-group">
              <div class="property-row">
                <label>填充颜色:</label>
                <el-color-picker
                  v-model="elementProps.fillColor"
                  size="small"
                  @change="updateElementProperty('fillColor', $event)"
                />
              </div>
              
              <div class="property-row">
                <label>边框颜色:</label>
                <el-color-picker
                  v-model="elementProps.borderColor"
                  size="small"
                  @change="updateElementProperty('borderColor', $event)"
                />
              </div>
              
              <div class="property-row">
                <label>边框宽度:</label>
                <el-input-number
                  v-model="elementProps.borderWidth"
                  size="small"
                  :min="0"
                  :step="1"
                  @change="updateElementProperty('borderWidth', $event)"
                />
              </div>
              
              <div class="property-row">
                <label>透明度:</label>
                <el-slider
                  v-model="elementProps.opacity"
                  :min="0"
                  :max="1"
                  :step="0.1"
                  @change="updateElementProperty('opacity', $event)"
                />
              </div>
            </div>
          </el-collapse-item>
          
          <el-collapse-item title="层级控制" name="layer">
            <div class="property-group">
              <div class="button-group">
                <el-button size="small" @click="bringToFront">置顶</el-button>
                <el-button size="small" @click="bringForward">上移</el-button>
                <el-button size="small" @click="sendBackward">下移</el-button>
                <el-button size="small" @click="sendToBack">置底</el-button>
              </div>
            </div>
          </el-collapse-item>
          
          <el-collapse-item title="事件配置" name="events">
            <div class="property-group">
              <EventConfigurator :element="selectedElements[0]" />
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
      
      <!-- 多个元素选中 -->
      <div v-else class="multiple-elements">
        <div class="element-info">
          <h4>已选中 {{ selectedElements.length }} 个元素</h4>
        </div>
        
        <div class="batch-operations">
          <el-button size="small" type="primary" @click="groupElements">
            <el-icon><Connection /></el-icon>
            组合
          </el-button>
          
          <el-button size="small" @click="alignLeft">
            <el-icon><AlignLeft /></el-icon>
            左对齐
          </el-button>
          
          <el-button size="small" @click="alignCenter">
            <el-icon><AlignCenter /></el-icon>
            居中对齐
          </el-button>
          
          <el-button size="small" @click="alignRight">
            <el-icon><AlignRight /></el-icon>
            右对齐
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, reactive, inject } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import {
  Select,
  Connection,
  AlignLeft,
  AlignCenter,
  AlignRight
} from '@element-plus/icons-vue'
import EventConfigurator from './EventConfigurator.vue'
import { EditorOperations } from '@/utils/editor'
import type { Meta2d } from '@/utils/meta2d'
import { ElMessage } from 'element-plus'

const canvasStore = useCanvasStore()
const activeCollapse = ref(['transform'])

// 注入Meta2d实例
const meta2d = inject<Meta2d>('meta2d')
let editorOps: EditorOperations | null = null

// 当Meta2d可用时创建编辑操作实例
watch(() => meta2d, (newMeta2d) => {
  if (newMeta2d) {
    editorOps = new EditorOperations(newMeta2d)
  }
}, { immediate: true })

// 选中的元素
const selectedElements = computed(() => canvasStore.selectedElements)

// 元素属性（用于双向绑定）
const elementProps = reactive({
  x: 0,
  y: 0,
  width: 100,
  height: 60,
  rotate: 0,
  fillColor: '#409eff',
  borderColor: '#409eff',
  borderWidth: 1,
  opacity: 1
})

// 监听选中元素变化，更新属性面板
watch(selectedElements, (elements) => {
  if (elements.length === 1) {
    const element = elements[0]
    Object.assign(elementProps, {
      x: element.x || 0,
      y: element.y || 0,
      width: element.width || 100,
      height: element.height || 60,
      rotate: element.rotate || 0,
      fillColor: element.fillColor || element.background || '#409eff',
      borderColor: element.borderColor || element.color || '#409eff',
      borderWidth: element.borderWidth || element.lineWidth || 1,
      opacity: element.opacity !== undefined ? element.opacity : 1
    })
  }
}, { deep: true, immediate: true })

// 获取元素类型名称
const getElementTypeName = (element: any) => {
  const typeMap: Record<string, string> = {
    'rectangle': '矩形',
    'circle': '圆形',
    'triangle': '三角形',
    'line': '直线',
    'image': '图片',
    'text': '文本'
  }
  return typeMap[element.name] || element.name || '未知'
}

// 更新元素属性
const updateElementProperty = (property: string, value: any) => {
  if (selectedElements.value.length === 1 && meta2d) {
    const element = selectedElements.value[0]
    meta2d.updatePen(element.id, { [property]: value })
    ElMessage.success(`已更新${property}属性`)
  }
}

// 层级控制
const bringToFront = () => {
  if (editorOps && selectedElements.value.length > 0) {
    editorOps.bringToFront(selectedElements.value)
    ElMessage.success('已置顶')
  }
}

const bringForward = () => {
  if (editorOps && selectedElements.value.length > 0) {
    editorOps.bringForward(selectedElements.value)
    ElMessage.success('已上移')
  }
}

const sendBackward = () => {
  if (editorOps && selectedElements.value.length > 0) {
    editorOps.sendBackward(selectedElements.value)
    ElMessage.success('已下移')
  }
}

const sendToBack = () => {
  if (editorOps && selectedElements.value.length > 0) {
    editorOps.sendToBack(selectedElements.value)
    ElMessage.success('已置底')
  }
}

// 批量操作
const groupElements = () => {
  if (editorOps && selectedElements.value.length > 1) {
    editorOps.group(selectedElements.value)
    ElMessage.success('已组合')
  }
}

const alignLeft = () => {
  if (editorOps && selectedElements.value.length > 1) {
    editorOps.align({ type: 'left', elements: selectedElements.value })
    ElMessage.success('已左对齐')
  }
}

const alignCenter = () => {
  if (editorOps && selectedElements.value.length > 1) {
    editorOps.align({ type: 'center', elements: selectedElements.value })
    ElMessage.success('已居中对齐')
  }
}

const alignRight = () => {
  if (editorOps && selectedElements.value.length > 1) {
    editorOps.align({ type: 'right', elements: selectedElements.value })
    ElMessage.success('已右对齐')
  }
}
</script>

<style scoped>
.properties-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.element-info {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.element-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.element-type {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

.property-group {
  padding: 12px 0;
}

.property-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.property-row label {
  font-size: 13px;
  color: #606266;
  min-width: 60px;
}

.button-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.batch-operations {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-top: 16px;
}

:deep(.el-collapse-item__header) {
  font-size: 13px;
  font-weight: 500;
}

:deep(.el-input-number) {
  width: 100px;
}

:deep(.el-input-number .el-input__inner) {
  text-align: right;
}
</style>
