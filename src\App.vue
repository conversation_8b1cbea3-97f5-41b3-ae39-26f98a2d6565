<template>
  <div class="editor-container">
    <!-- 顶部菜单栏 -->
    <div class="menu-bar">
      <div class="menu-left">
        <h1 class="app-title">可视化编辑器</h1>
      </div>
      <div class="menu-right">
        <el-button size="small" @click="showProjectManager = true">
          <el-icon><Folder /></el-icon>
          项目管理
        </el-button>
        <el-button size="small" @click="showPerformancePanel = !showPerformancePanel">
          <el-icon><Monitor /></el-icon>
          性能监控
        </el-button>
      </div>
    </div>

    <!-- 性能监控面板 -->
    <el-collapse-transition>
      <div v-show="showPerformancePanel" class="performance-panel">
        <PerformancePanel />
      </div>
    </el-collapse-transition>

    <!-- 主编辑区域 -->
    <div class="main-content">
      <!-- 组件库面板 -->
      <div class="component-panel">
        <ComponentLibrary />
      </div>

      <!-- 画布区域 -->
      <div class="canvas-container">
        <!-- 工具栏 -->
        <div class="toolbar">
          <Toolbar />
        </div>

        <!-- 画布区域 -->
        <div class="canvas-wrapper">
          <CanvasEditor />
        </div>
      </div>

      <!-- 属性面板 -->
      <div class="properties-panel">
        <PropertiesPanel />
      </div>
    </div>

    <!-- 项目管理对话框 -->
    <ProjectManager v-model="showProjectManager" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Folder, Monitor } from '@element-plus/icons-vue'
import ComponentLibrary from './components/ComponentLibrary.vue'
import Toolbar from './components/Toolbar.vue'
import CanvasEditor from './components/CanvasEditor.vue'
import PropertiesPanel from './components/PropertiesPanel.vue'
import ProjectManager from './components/ProjectManager.vue'
import PerformancePanel from './components/PerformancePanel.vue'

const showProjectManager = ref(false)
const showPerformancePanel = ref(false)
</script>

<style scoped>
.editor-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.menu-bar {
  height: 50px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  z-index: 100;
}

.menu-left {
  display: flex;
  align-items: center;
}

.app-title {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.menu-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.performance-panel {
  background: white;
  border-bottom: 1px solid #e4e7ed;
  padding: 12px 16px;
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.component-panel {
  width: 280px;
  background: white;
  border-right: 1px solid #e4e7ed;
  overflow-y: auto;
}

.canvas-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

.toolbar {
  height: 50px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  padding: 0 16px;
  gap: 12px;
}

.canvas-wrapper {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: #f0f0f0;
}

.properties-panel {
  width: 300px;
  background: white;
  border-left: 1px solid #e4e7ed;
  overflow-y: auto;
}
</style>
