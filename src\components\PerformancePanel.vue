<template>
  <div class="performance-panel">
    <div class="performance-metrics">
      <div class="metric-item">
        <span class="metric-label">帧率:</span>
        <span class="metric-value" :class="getFrameRateClass()">
          {{ metrics.frameRate }} FPS
        </span>
      </div>
      
      <div class="metric-item">
        <span class="metric-label">渲染时间:</span>
        <span class="metric-value" :class="getRenderTimeClass()">
          {{ metrics.renderTime.toFixed(2) }} ms
        </span>
      </div>
      
      <div class="metric-item">
        <span class="metric-label">内存使用:</span>
        <span class="metric-value">
          {{ metrics.memoryUsage.toFixed(1) }} MB
        </span>
      </div>
      
      <div class="metric-item">
        <span class="metric-label">元素数量:</span>
        <span class="metric-value">
          {{ metrics.elementCount }}
        </span>
      </div>
      
      <div class="metric-item">
        <span class="metric-label">性能等级:</span>
        <el-tag :type="getPerformanceTagType()" size="small">
          {{ getPerformanceLevelText() }}
        </el-tag>
      </div>
    </div>
    
    <div class="performance-actions">
      <el-button size="small" @click="resetMetrics">
        <el-icon><Refresh /></el-icon>
        重置
      </el-button>
      
      <el-button size="small" @click="toggleAutoUpdate">
        <el-icon><Timer /></el-icon>
        {{ autoUpdate ? '停止' : '开始' }}监控
      </el-button>
      
      <el-button size="small" @click="exportMetrics">
        <el-icon><Download /></el-icon>
        导出数据
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { Refresh, Timer, Download } from '@element-plus/icons-vue'
import { performanceMonitor, type PerformanceMetrics } from '@/utils/performance'
import { ElMessage } from 'element-plus'

const metrics = ref<PerformanceMetrics>({
  renderTime: 0,
  frameRate: 0,
  memoryUsage: 0,
  elementCount: 0,
  lastUpdate: Date.now()
})

const autoUpdate = ref(true)
let updateTimer: number | null = null

// 更新性能指标
const updateMetrics = () => {
  metrics.value = performanceMonitor.getMetrics()
}

// 重置指标
const resetMetrics = () => {
  performanceMonitor.reset()
  updateMetrics()
  ElMessage.success('性能指标已重置')
}

// 切换自动更新
const toggleAutoUpdate = () => {
  autoUpdate.value = !autoUpdate.value
  
  if (autoUpdate.value) {
    startAutoUpdate()
    ElMessage.success('性能监控已开启')
  } else {
    stopAutoUpdate()
    ElMessage.success('性能监控已停止')
  }
}

// 开始自动更新
const startAutoUpdate = () => {
  if (updateTimer) return
  
  updateTimer = window.setInterval(() => {
    updateMetrics()
  }, 1000) // 每秒更新一次
}

// 停止自动更新
const stopAutoUpdate = () => {
  if (updateTimer) {
    clearInterval(updateTimer)
    updateTimer = null
  }
}

// 导出性能数据
const exportMetrics = () => {
  const data = {
    timestamp: Date.now(),
    metrics: metrics.value,
    performanceLevel: performanceMonitor.getPerformanceLevel()
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `performance-metrics-${Date.now()}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  ElMessage.success('性能数据导出成功')
}

// 获取帧率样式类
const getFrameRateClass = () => {
  const fps = metrics.value.frameRate
  if (fps >= 55) return 'metric-excellent'
  if (fps >= 45) return 'metric-good'
  if (fps >= 30) return 'metric-fair'
  return 'metric-poor'
}

// 获取渲染时间样式类
const getRenderTimeClass = () => {
  const time = metrics.value.renderTime
  if (time < 5) return 'metric-excellent'
  if (time < 10) return 'metric-good'
  if (time < 20) return 'metric-fair'
  return 'metric-poor'
}

// 获取性能等级文本
const getPerformanceLevelText = () => {
  const level = performanceMonitor.getPerformanceLevel()
  const texts = {
    excellent: '优秀',
    good: '良好',
    fair: '一般',
    poor: '较差'
  }
  return texts[level]
}

// 获取性能标签类型
const getPerformanceTagType = () => {
  const level = performanceMonitor.getPerformanceLevel()
  const types = {
    excellent: 'success',
    good: 'success',
    fair: 'warning',
    poor: 'danger'
  }
  return types[level] as any
}

// 组件挂载
onMounted(() => {
  updateMetrics()
  if (autoUpdate.value) {
    startAutoUpdate()
  }
})

// 组件卸载
onUnmounted(() => {
  stopAutoUpdate()
})
</script>

<style scoped>
.performance-panel {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
}

.performance-metrics {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
}

.metric-label {
  color: #606266;
  font-weight: 500;
}

.metric-value {
  font-family: monospace;
  font-weight: 600;
}

.metric-excellent {
  color: #67c23a;
}

.metric-good {
  color: #409eff;
}

.metric-fair {
  color: #e6a23c;
}

.metric-poor {
  color: #f56c6c;
}

.performance-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
