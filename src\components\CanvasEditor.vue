<template>
  <div class="canvas-editor" ref="editorRef">
    <!-- 画布容器 -->
    <div 
      class="canvas-container"
      :style="containerStyle"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragenter="handleDragEnter"
    >
      <!-- 网格背景 -->
      <canvas
        v-if="canvasStore.gridEnabled"
        ref="gridCanvasRef"
        class="grid-canvas"
        :width="canvasStore.size.width * canvasStore.zoom"
        :height="canvasStore.size.height * canvasStore.zoom"
      ></canvas>
      
      <!-- 参考线 -->
      <div v-if="canvasStore.showGuidelines" class="guidelines">
        <div
          v-for="x in canvasStore.guidelines.x"
          :key="`x-${x}`"
          class="guideline vertical"
          :style="{ left: x * canvasStore.zoom + 'px' }"
        ></div>
        <div
          v-for="y in canvasStore.guidelines.y"
          :key="`y-${y}`"
          class="guideline horizontal"
          :style="{ top: y * canvasStore.zoom + 'px' }"
        ></div>
      </div>
      
      <!-- Meta2d画布 -->
      <div 
        ref="meta2dRef" 
        class="meta2d-canvas"
        :style="canvasStyle"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick, provide } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import { useComponentStore } from '@/stores/components'
import { createMeta2d, type Meta2d, type Meta2dElement } from '@/utils/meta2d'
import { KeyboardManager, createDefaultShortcuts } from '@/utils/keyboard'
import { animationManager } from '@/utils/animation'
import { GridSystem } from '@/utils/grid'
import { ElMessage } from 'element-plus'

const canvasStore = useCanvasStore()
const componentStore = useComponentStore()

const editorRef = ref<HTMLElement>()
const meta2dRef = ref<HTMLElement>()
const gridCanvasRef = ref<HTMLCanvasElement>()
let meta2d: Meta2d | null = null
let keyboardManager: KeyboardManager | null = null
let gridSystem: GridSystem | null = null

// 容器样式
const containerStyle = computed(() => ({
  transform: `translate(${canvasStore.position.x}px, ${canvasStore.position.y}px)`,
  transformOrigin: 'center center'
}))

// 画布样式
const canvasStyle = computed(() => ({
  width: canvasStore.size.width * canvasStore.zoom + 'px',
  height: canvasStore.size.height * canvasStore.zoom + 'px',
  backgroundColor: canvasStore.background.color,
  backgroundImage: canvasStore.background.image ? `url(${canvasStore.background.image})` : 'none',
  backgroundSize: getBackgroundSize(),
  backgroundRepeat: getBackgroundRepeat(),
  backgroundPosition: 'center center'
}))

// 获取背景尺寸
function getBackgroundSize() {
  const mode = canvasStore.background.mode
  switch (mode) {
    case 'original': return 'auto'
    case 'stretch': return '100% 100%'
    case 'fit': return 'contain'
    case 'cover': return 'cover'
    case 'contain': return 'contain'
    default: return 'auto'
  }
}

// 获取背景重复
function getBackgroundRepeat() {
  const mode = canvasStore.background.mode
  switch (mode) {
    case 'repeat': return 'repeat'
    case 'repeat-x': return 'repeat-x'
    case 'repeat-y': return 'repeat-y'
    case 'no-repeat': return 'no-repeat'
    default: return 'no-repeat'
  }
}

// 初始化Meta2d
const initMeta2d = async () => {
  if (!meta2dRef.value) return

  try {
    meta2d = createMeta2d({
      canvas: meta2dRef.value,
      width: canvasStore.size.width,
      height: canvasStore.size.height,
      color: '#ffffff',
      activeColor: '#409eff',
      hoverColor: '#67c23a',
      grid: false, // 使用自定义网格
      rule: false,
      scroll: false
    })

    // 监听画布事件
    meta2d.on('add', handleElementAdd)
    meta2d.on('active', handleElementActive)
    meta2d.on('inactive', handleElementInactive)
    meta2d.on('moveOut', handleElementMoveOut)

    // 提供Meta2d实例给子组件
    provide('meta2d', meta2d)

    console.log('Meta2d initialized successfully')
  } catch (error) {
    console.error('Failed to initialize Meta2d:', error)
    ElMessage.error('画布初始化失败')
  }
}

// 初始化网格系统
const initGridSystem = () => {
  if (!gridCanvasRef.value) return

  gridSystem = new GridSystem(gridCanvasRef.value)

  // 设置网格选项
  gridSystem.setGridOptions({
    size: canvasStore.gridSize,
    enabled: canvasStore.gridEnabled,
    color: '#e0e0e0',
    opacity: 0.5
  })

  // 设置参考线选项
  gridSystem.setGuidelineOptions({
    enabled: canvasStore.showGuidelines,
    color: '#409eff',
    opacity: 0.8
  })

  // 设置吸附选项
  gridSystem.setSnapOptions({
    enabled: canvasStore.snapToGrid,
    threshold: 5
  })

  // 同步参考线
  canvasStore.guidelines.x.forEach(x => gridSystem!.addGuideline('x', x))
  canvasStore.guidelines.y.forEach(y => gridSystem!.addGuideline('y', y))

  gridSystem.render()
}

// 更新网格
const updateGrid = () => {
  if (!gridSystem) return

  gridSystem.setGridOptions({
    size: canvasStore.gridSize,
    enabled: canvasStore.gridEnabled
  })

  gridSystem.setGuidelineOptions({
    enabled: canvasStore.showGuidelines
  })

  gridSystem.setSnapOptions({
    enabled: canvasStore.snapToGrid
  })
}

// 处理拖拽进入
const handleDragEnter = (e: DragEvent) => {
  e.preventDefault()
}

// 处理拖拽悬停
const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  if (e.dataTransfer) {
    e.dataTransfer.dropEffect = 'copy'
  }
}

// 处理拖拽放置
const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  
  if (!e.dataTransfer || !meta2d) return
  
  try {
    const componentData = JSON.parse(e.dataTransfer.getData('application/json'))
    const rect = meta2dRef.value?.getBoundingClientRect()
    
    if (!rect) return
    
    // 计算相对于画布的坐标
    let x = (e.clientX - rect.left) / canvasStore.zoom
    let y = (e.clientY - rect.top) / canvasStore.zoom

    // 应用吸附
    if (gridSystem && canvasStore.snapToGrid) {
      const snapped = gridSystem.snap(x, y)
      x = snapped.x
      y = snapped.y
    }

    // 创建元素
    const element = createElementFromComponent(componentData, x, y)
    if (element) {
      meta2d.addPen(element)
      ElMessage.success(`已添加 ${componentData.name}`)
    }
  } catch (error) {
    console.error('Failed to add component:', error)
    ElMessage.error('添加组件失败')
  }
}

// 从组件数据创建元素
const createElementFromComponent = (component: any, x: number, y: number): Meta2dElement => {
  const defaultProps = component.defaultProps || {}

  const element: Meta2dElement = {
    id: `${component.id}_${Date.now()}`,
    name: component.meta2dType || 'rectangle',
    x: x - (defaultProps.width || 100) / 2,
    y: y - (defaultProps.height || 60) / 2,
    width: defaultProps.width || 100,
    height: defaultProps.height || 60,
    fillColor: defaultProps.fillColor || '#409eff',
    borderColor: defaultProps.borderColor || '#409eff',
    borderWidth: defaultProps.borderWidth || 1,
    opacity: defaultProps.opacity !== undefined ? defaultProps.opacity : 1,
    visible: true,
    ...defaultProps
  }

  // 如果有SVG，设置为图片类型
  if (component.svg) {
    element.name = 'image'
    element.image = 'data:image/svg+xml;base64,' + btoa(component.svg)
  }

  return element
}

// 元素添加事件
const handleElementAdd = (element: any) => {
  console.log('Element added:', element)
}

// 元素激活事件
const handleElementActive = (elements: any[]) => {
  canvasStore.selectElements(elements)
}

// 元素取消激活事件
const handleElementInactive = () => {
  canvasStore.clearSelection()
}

// 元素移出事件
const handleElementMoveOut = () => {
  // 可以在这里处理元素移出画布的逻辑
}

// 监听画布尺寸变化
watch(() => canvasStore.size, () => {
  if (meta2d) {
    meta2d.resize(canvasStore.size.width, canvasStore.size.height)
  }
  if (gridSystem) {
    gridSystem.resize(
      canvasStore.size.width * canvasStore.zoom,
      canvasStore.size.height * canvasStore.zoom
    )
  }
}, { deep: true })

// 监听缩放变化
watch(() => canvasStore.zoom, () => {
  if (meta2d) {
    meta2d.scale(canvasStore.zoom)
  }
  if (gridSystem) {
    gridSystem.resize(
      canvasStore.size.width * canvasStore.zoom,
      canvasStore.size.height * canvasStore.zoom
    )
  }
})

// 监听网格和参考线设置变化
watch(() => [
  canvasStore.gridEnabled,
  canvasStore.gridSize,
  canvasStore.showGuidelines,
  canvasStore.snapToGrid
], () => {
  updateGrid()
}, { deep: true })

// 监听参考线变化
watch(() => canvasStore.guidelines, (newGuidelines, oldGuidelines) => {
  if (!gridSystem) return

  // 清除旧的参考线
  if (oldGuidelines) {
    gridSystem.clearGuidelines()
  }

  // 添加新的参考线
  newGuidelines.x.forEach(x => gridSystem!.addGuideline('x', x))
  newGuidelines.y.forEach(y => gridSystem!.addGuideline('y', y))
}, { deep: true })

// 初始化键盘快捷键
const initKeyboardShortcuts = () => {
  keyboardManager = new KeyboardManager()

  const shortcuts = createDefaultShortcuts({
    selectAll: () => {
      if (meta2d) {
        meta2d.selectAll()
      }
    },
    copy: () => {
      if (meta2d) {
        meta2d.copy()
        ElMessage.success('已复制')
      }
    },
    cut: () => {
      if (meta2d) {
        meta2d.cut()
        ElMessage.success('已剪切')
      }
    },
    paste: () => {
      if (meta2d) {
        meta2d.paste()
        ElMessage.success('已粘贴')
      }
    },
    undo: () => {
      if (meta2d) {
        meta2d.undo()
        ElMessage.success('已撤销')
      }
    },
    redo: () => {
      if (meta2d) {
        meta2d.redo()
        ElMessage.success('已重做')
      }
    },
    delete: () => {
      const selectedElements = canvasStore.selectedElements
      if (selectedElements.length > 0 && meta2d) {
        selectedElements.forEach(element => {
          meta2d!.deletePen(element)
        })
        ElMessage.success(`已删除 ${selectedElements.length} 个元素`)
      }
    },
    duplicate: () => {
      // TODO: 实现复制元素功能
      ElMessage.info('复制元素功能待实现')
    },
    group: () => {
      if (meta2d && canvasStore.selectedElements.length > 1) {
        meta2d.combine()
        ElMessage.success('已组合')
      }
    },
    ungroup: () => {
      if (meta2d && canvasStore.selectedElements.length === 1) {
        meta2d.uncombine()
        ElMessage.success('已取消组合')
      }
    },
    bringToFront: () => {
      if (meta2d && canvasStore.selectedElements.length > 0) {
        meta2d.bringToFront()
        ElMessage.success('已置顶')
      }
    },
    sendToBack: () => {
      if (meta2d && canvasStore.selectedElements.length > 0) {
        meta2d.sendToBack()
        ElMessage.success('已置底')
      }
    },
    save: () => {
      // TODO: 实现保存功能
      ElMessage.info('保存功能待实现')
    }
  })

  keyboardManager.registerMultiple(shortcuts)
  keyboardManager.startListening()
}

// 组件挂载
onMounted(async () => {
  await nextTick()
  await initMeta2d()
  initKeyboardShortcuts()
  initGridSystem()
})

// 组件卸载
onUnmounted(() => {
  if (meta2d) {
    meta2d.destroy()
  }
  if (keyboardManager) {
    keyboardManager.stopListening()
  }
  if (gridSystem) {
    gridSystem.destroy()
  }
  // 停止所有动画
  animationManager.stopAll()
})
</script>

<style scoped>
.canvas-editor {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.canvas-container {
  position: relative;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.grid-canvas {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1;
}

.guidelines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}

.guideline {
  position: absolute;
  background: #409eff;
  opacity: 0.6;
}

.guideline.horizontal {
  width: 100%;
  height: 1px;
}

.guideline.vertical {
  width: 1px;
  height: 100%;
}

.meta2d-canvas {
  position: relative;
  z-index: 3;
  border: 1px solid #ddd;
}
</style>
