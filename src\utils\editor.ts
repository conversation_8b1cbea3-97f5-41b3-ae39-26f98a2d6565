// 编辑器操作工具类
import type { Meta2d, Meta2dElement } from './meta2d'
import { animationManager } from './animation'

export interface AlignmentOptions {
  type: 'left' | 'center' | 'right' | 'top' | 'middle' | 'bottom'
  elements: Meta2dElement[]
}

export interface DistributionOptions {
  type: 'horizontal' | 'vertical'
  elements: Meta2dElement[]
}

export class EditorOperations {
  private meta2d: Meta2d
  private clipboard: Meta2dElement[] = []

  constructor(meta2d: Meta2d) {
    this.meta2d = meta2d
  }

  // 复制元素
  copy(elements: Meta2dElement[] = []) {
    const selectedElements = elements.length > 0 ? elements : this.meta2d.getSelectedElements()
    if (selectedElements.length === 0) return

    // 深拷贝元素数据
    this.clipboard = selectedElements.map(element => ({
      ...element,
      id: `${element.id}_copy_${Date.now()}`
    }))
  }

  // 剪切元素
  cut(elements: Meta2dElement[] = []) {
    const selectedElements = elements.length > 0 ? elements : this.meta2d.getSelectedElements()
    if (selectedElements.length === 0) return

    this.copy(selectedElements)
    this.delete(selectedElements)
  }

  // 粘贴元素
  paste(offsetX: number = 20, offsetY: number = 20) {
    if (this.clipboard.length === 0) return

    const pastedElements: Meta2dElement[] = []

    this.clipboard.forEach(element => {
      const newElement: Meta2dElement = {
        ...element,
        id: `${element.id}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        x: element.x + offsetX,
        y: element.y + offsetY
      }

      this.meta2d.addPen(newElement)
      pastedElements.push(newElement)
    })

    return pastedElements
  }

  // 删除元素
  delete(elements: Meta2dElement[] = []) {
    const selectedElements = elements.length > 0 ? elements : this.meta2d.getSelectedElements()
    if (selectedElements.length === 0) return

    selectedElements.forEach(element => {
      this.meta2d.deletePen(element)
    })
  }

  // 复制元素（在原位置附近）
  duplicate(elements: Meta2dElement[] = [], offsetX: number = 20, offsetY: number = 20) {
    const selectedElements = elements.length > 0 ? elements : this.meta2d.getSelectedElements()
    if (selectedElements.length === 0) return

    const duplicatedElements: Meta2dElement[] = []

    selectedElements.forEach(element => {
      const newElement: Meta2dElement = {
        ...element,
        id: `${element.id}_dup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        x: element.x + offsetX,
        y: element.y + offsetY
      }

      this.meta2d.addPen(newElement)
      duplicatedElements.push(newElement)
    })

    return duplicatedElements
  }

  // 对齐元素
  align(options: AlignmentOptions) {
    const { type, elements } = options
    if (elements.length < 2) return

    const bounds = this.getBounds(elements)

    elements.forEach(element => {
      switch (type) {
        case 'left':
          element.x = bounds.left
          break
        case 'center':
          element.x = bounds.left + (bounds.width - element.width) / 2
          break
        case 'right':
          element.x = bounds.right - element.width
          break
        case 'top':
          element.y = bounds.top
          break
        case 'middle':
          element.y = bounds.top + (bounds.height - element.height) / 2
          break
        case 'bottom':
          element.y = bounds.bottom - element.height
          break
      }

      this.meta2d.updatePen(element.id, { x: element.x, y: element.y })
    })

    this.meta2d.render()
  }

  // 分布元素
  distribute(options: DistributionOptions) {
    const { type, elements } = options
    if (elements.length < 3) return

    // 按位置排序
    const sortedElements = [...elements].sort((a, b) => {
      return type === 'horizontal' ? a.x - b.x : a.y - b.y
    })

    const first = sortedElements[0]
    const last = sortedElements[sortedElements.length - 1]

    if (type === 'horizontal') {
      const totalWidth = last.x + last.width - first.x
      const availableSpace = totalWidth - sortedElements.reduce((sum, el) => sum + el.width, 0)
      const spacing = availableSpace / (sortedElements.length - 1)

      let currentX = first.x + first.width

      for (let i = 1; i < sortedElements.length - 1; i++) {
        const element = sortedElements[i]
        element.x = currentX + spacing
        currentX = element.x + element.width
        this.meta2d.updatePen(element.id, { x: element.x })
      }
    } else {
      const totalHeight = last.y + last.height - first.y
      const availableSpace = totalHeight - sortedElements.reduce((sum, el) => sum + el.height, 0)
      const spacing = availableSpace / (sortedElements.length - 1)

      let currentY = first.y + first.height

      for (let i = 1; i < sortedElements.length - 1; i++) {
        const element = sortedElements[i]
        element.y = currentY + spacing
        currentY = element.y + element.height
        this.meta2d.updatePen(element.id, { y: element.y })
      }
    }

    this.meta2d.render()
  }

  // 层级操作
  bringToFront(elements: Meta2dElement[] = []) {
    const selectedElements = elements.length > 0 ? elements : this.meta2d.getSelectedElements()
    selectedElements.forEach(() => {
      this.meta2d.bringToFront()
    })
  }

  sendToBack(elements: Meta2dElement[] = []) {
    const selectedElements = elements.length > 0 ? elements : this.meta2d.getSelectedElements()
    selectedElements.forEach(() => {
      this.meta2d.sendToBack()
    })
  }

  bringForward(elements: Meta2dElement[] = []) {
    const selectedElements = elements.length > 0 ? elements : this.meta2d.getSelectedElements()
    selectedElements.forEach(() => {
      this.meta2d.bringForward()
    })
  }

  sendBackward(elements: Meta2dElement[] = []) {
    const selectedElements = elements.length > 0 ? elements : this.meta2d.getSelectedElements()
    selectedElements.forEach(() => {
      this.meta2d.sendBackward()
    })
  }

  // 组合元素
  group(elements: Meta2dElement[] = []) {
    const selectedElements = elements.length > 0 ? elements : this.meta2d.getSelectedElements()
    if (selectedElements.length < 2) return

    this.meta2d.combine()
  }

  // 取消组合
  ungroup(elements: Meta2dElement[] = []) {
    const selectedElements = elements.length > 0 ? elements : this.meta2d.getSelectedElements()
    if (selectedElements.length !== 1) return

    this.meta2d.uncombine()
  }

  // 锁定/解锁元素
  lock(elements: Meta2dElement[] = []) {
    const selectedElements = elements.length > 0 ? elements : this.meta2d.getSelectedElements()
    selectedElements.forEach(element => {
      this.meta2d.updatePen(element.id, { locked: true })
    })
  }

  unlock(elements: Meta2dElement[] = []) {
    const selectedElements = elements.length > 0 ? elements : this.meta2d.getSelectedElements()
    selectedElements.forEach(element => {
      this.meta2d.updatePen(element.id, { locked: false })
    })
  }

  // 显示/隐藏元素
  show(elements: Meta2dElement[] = []) {
    const selectedElements = elements.length > 0 ? elements : this.meta2d.getSelectedElements()
    selectedElements.forEach(element => {
      this.meta2d.updatePen(element.id, { visible: true })
    })
  }

  hide(elements: Meta2dElement[] = []) {
    const selectedElements = elements.length > 0 ? elements : this.meta2d.getSelectedElements()
    selectedElements.forEach(element => {
      this.meta2d.updatePen(element.id, { visible: false })
    })
  }

  // 获取元素边界
  private getBounds(elements: Meta2dElement[]) {
    if (elements.length === 0) {
      return { left: 0, top: 0, right: 0, bottom: 0, width: 0, height: 0 }
    }

    let left = elements[0].x
    let top = elements[0].y
    let right = elements[0].x + elements[0].width
    let bottom = elements[0].y + elements[0].height

    elements.forEach(element => {
      left = Math.min(left, element.x)
      top = Math.min(top, element.y)
      right = Math.max(right, element.x + element.width)
      bottom = Math.max(bottom, element.y + element.height)
    })

    return {
      left,
      top,
      right,
      bottom,
      width: right - left,
      height: bottom - top
    }
  }

  // 应用动画到元素
  animateElements(elements: Meta2dElement[], animationType: string, config: any = {}) {
    elements.forEach(element => {
      animationManager.playPreset(element, animationType, {
        ...config,
        onUpdate: () => {
          this.meta2d.render()
        }
      })
    })
  }
}
