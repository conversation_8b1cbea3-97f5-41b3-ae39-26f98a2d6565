// 网格和参考线工具类

export interface GridOptions {
  size: number
  color: string
  opacity: number
  enabled: boolean
}

export interface GuidelineOptions {
  color: string
  opacity: number
  enabled: boolean
}

export interface SnapOptions {
  enabled: boolean
  threshold: number // 吸附阈值（像素）
}

export class GridSystem {
  private canvas: HTMLCanvasElement
  private ctx: CanvasRenderingContext2D
  private gridOptions: GridOptions
  private guidelineOptions: GuidelineOptions
  private snapOptions: SnapOptions
  private guidelines: { x: number[], y: number[] }

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas
    const ctx = canvas.getContext('2d')
    if (!ctx) {
      throw new Error('Cannot get 2D context from canvas')
    }
    this.ctx = ctx

    // 默认配置
    this.gridOptions = {
      size: 20,
      color: '#e0e0e0',
      opacity: 0.5,
      enabled: true
    }

    this.guidelineOptions = {
      color: '#409eff',
      opacity: 0.8,
      enabled: true
    }

    this.snapOptions = {
      enabled: true,
      threshold: 5
    }

    this.guidelines = { x: [], y: [] }
  }

  // 设置网格选项
  setGridOptions(options: Partial<GridOptions>) {
    Object.assign(this.gridOptions, options)
    this.render()
  }

  // 设置参考线选项
  setGuidelineOptions(options: Partial<GuidelineOptions>) {
    Object.assign(this.guidelineOptions, options)
    this.render()
  }

  // 设置吸附选项
  setSnapOptions(options: Partial<SnapOptions>) {
    Object.assign(this.snapOptions, options)
  }

  // 添加参考线
  addGuideline(type: 'x' | 'y', value: number) {
    if (type === 'x' && !this.guidelines.x.includes(value)) {
      this.guidelines.x.push(value)
    } else if (type === 'y' && !this.guidelines.y.includes(value)) {
      this.guidelines.y.push(value)
    }
    this.render()
  }

  // 移除参考线
  removeGuideline(type: 'x' | 'y', value: number) {
    if (type === 'x') {
      const index = this.guidelines.x.indexOf(value)
      if (index > -1) this.guidelines.x.splice(index, 1)
    } else {
      const index = this.guidelines.y.indexOf(value)
      if (index > -1) this.guidelines.y.splice(index, 1)
    }
    this.render()
  }

  // 清除所有参考线
  clearGuidelines() {
    this.guidelines.x = []
    this.guidelines.y = []
    this.render()
  }

  // 获取参考线
  getGuidelines() {
    return { ...this.guidelines }
  }

  // 渲染网格和参考线
  render() {
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)

    if (this.gridOptions.enabled) {
      this.drawGrid()
    }

    if (this.guidelineOptions.enabled) {
      this.drawGuidelines()
    }
  }

  // 绘制网格
  private drawGrid() {
    const { size, color, opacity } = this.gridOptions
    const { width, height } = this.canvas

    this.ctx.save()
    this.ctx.strokeStyle = color
    this.ctx.globalAlpha = opacity
    this.ctx.lineWidth = 1

    // 绘制垂直线
    for (let x = 0; x <= width; x += size) {
      this.ctx.beginPath()
      this.ctx.moveTo(x + 0.5, 0)
      this.ctx.lineTo(x + 0.5, height)
      this.ctx.stroke()
    }

    // 绘制水平线
    for (let y = 0; y <= height; y += size) {
      this.ctx.beginPath()
      this.ctx.moveTo(0, y + 0.5)
      this.ctx.lineTo(width, y + 0.5)
      this.ctx.stroke()
    }

    this.ctx.restore()
  }

  // 绘制参考线
  private drawGuidelines() {
    const { color, opacity } = this.guidelineOptions
    const { width, height } = this.canvas

    this.ctx.save()
    this.ctx.strokeStyle = color
    this.ctx.globalAlpha = opacity
    this.ctx.lineWidth = 1
    this.ctx.setLineDash([5, 5])

    // 绘制垂直参考线
    this.guidelines.x.forEach(x => {
      this.ctx.beginPath()
      this.ctx.moveTo(x + 0.5, 0)
      this.ctx.lineTo(x + 0.5, height)
      this.ctx.stroke()
    })

    // 绘制水平参考线
    this.guidelines.y.forEach(y => {
      this.ctx.beginPath()
      this.ctx.moveTo(0, y + 0.5)
      this.ctx.lineTo(width, y + 0.5)
      this.ctx.stroke()
    })

    this.ctx.restore()
  }

  // 吸附到网格
  snapToGrid(x: number, y: number): { x: number, y: number } {
    if (!this.snapOptions.enabled || !this.gridOptions.enabled) {
      return { x, y }
    }

    const { size } = this.gridOptions
    const snappedX = Math.round(x / size) * size
    const snappedY = Math.round(y / size) * size

    return { x: snappedX, y: snappedY }
  }

  // 吸附到参考线
  snapToGuidelines(x: number, y: number): { x: number, y: number, snapped: boolean } {
    if (!this.snapOptions.enabled || !this.guidelineOptions.enabled) {
      return { x, y, snapped: false }
    }

    const { threshold } = this.snapOptions
    let snappedX = x
    let snappedY = y
    let snapped = false

    // 检查垂直参考线
    for (const guideX of this.guidelines.x) {
      if (Math.abs(x - guideX) <= threshold) {
        snappedX = guideX
        snapped = true
        break
      }
    }

    // 检查水平参考线
    for (const guideY of this.guidelines.y) {
      if (Math.abs(y - guideY) <= threshold) {
        snappedY = guideY
        snapped = true
        break
      }
    }

    return { x: snappedX, y: snappedY, snapped }
  }

  // 综合吸附（网格和参考线）
  snap(x: number, y: number): { x: number, y: number, snapped: boolean } {
    if (!this.snapOptions.enabled) {
      return { x, y, snapped: false }
    }

    // 先尝试吸附到参考线
    const guidelineSnap = this.snapToGuidelines(x, y)
    if (guidelineSnap.snapped) {
      return guidelineSnap
    }

    // 如果没有吸附到参考线，尝试吸附到网格
    const gridSnap = this.snapToGrid(x, y)
    const snapped = gridSnap.x !== x || gridSnap.y !== y

    return { ...gridSnap, snapped }
  }

  // 检测是否接近参考线（用于显示吸附提示）
  isNearGuideline(x: number, y: number): { near: boolean, type?: 'x' | 'y', value?: number } {
    const { threshold } = this.snapOptions

    // 检查垂直参考线
    for (const guideX of this.guidelines.x) {
      if (Math.abs(x - guideX) <= threshold) {
        return { near: true, type: 'x', value: guideX }
      }
    }

    // 检查水平参考线
    for (const guideY of this.guidelines.y) {
      if (Math.abs(y - guideY) <= threshold) {
        return { near: true, type: 'y', value: guideY }
      }
    }

    return { near: false }
  }

  // 更新画布尺寸
  resize(width: number, height: number) {
    this.canvas.width = width
    this.canvas.height = height
    this.render()
  }

  // 销毁
  destroy() {
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)
  }
}
