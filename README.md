# 可视化编辑器

基于 Vue 3 + Pixi.js + Pinia + GSAP + Meta2d.js 的可视化编辑器，支持 DataV 组件编辑、事件驱动动画和跨端画布配置。

## 🚀 技术栈

- **Vue 3** - 现代化的前端框架
- **Pinia** - Vue 3 官方状态管理库
- **GSAP** - 高性能动画库
- **Meta2d.js** - 2D 图形编辑引擎
- **Pixi.js** - 2D WebGL 渲染器
- **Element Plus** - Vue 3 UI 组件库
- **TypeScript** - 类型安全的 JavaScript
- **Vite** - 快速的构建工具

## ✨ 核心功能

### 🎨 画布系统
- **多尺寸预设**: 支持 PC 端和移动端多种尺寸预设
- **自定义尺寸**: 支持自定义画布尺寸
- **缩放控制**: 支持 0.5x - 5x 缩放，滑块精确控制
- **背景管理**: 颜色选择器 + 背景图上传 + 9种显示模式
- **坐标系统**: 实时显示画布坐标偏移量

### 📦 组件库系统
- **分类管理**: 电气符号、DataV边框、DataV装饰、基础图形
- **拖拽添加**: 支持拖拽方式添加组件到画布
- **组件搜索**: 快速搜索和筛选组件
- **SVG 支持**: 完整的 SVG 图形支持

### ⚡ 编辑操作
- **快捷键支持**: 完整的键盘快捷键系统
  - `Ctrl+A` 全选
  - `Ctrl+C/V/X` 复制/粘贴/剪切
  - `Ctrl+Z/Y` 撤销/重做
  - `Ctrl+G/U` 组合/取消组合
  - `Delete` 删除选中元素
- **图形操作**: 组合、取消组合、层级控制
- **对齐功能**: 左对齐、居中对齐、右对齐
- **选择和移动**: 多选、框选、精确移动

### 🛠️ 辅助工具
- **网格系统**: 可调节网格大小和显示状态
- **参考线**: 动态参考线系统
- **吸附功能**: 自动对齐网格和参考线
- **标尺显示**: 精确的标尺和坐标显示

### 🎭 事件系统
- **事件类型**: click、mouseover、mouseout、dblclick 等
- **动作支持**:
  - `changeProp` - 改变属性
  - `playAnimation` - 播放动画
  - `navigate` - 页面跳转
  - `toggleVisible` - 显示/隐藏
  - `sendMessage` - 发送消息
  - `executeScript` - 执行脚本
- **全局事件**: 页面加载、画布变更等全局钩子

### 💾 状态管理
- **持久化存储**: 自动保存画布状态到本地存储
- **项目管理**: 创建、保存、加载、导出项目
- **状态同步**: 实时同步所有组件状态
- **历史记录**: 操作历史和撤销重做

### 🚀 性能优化
- **增量渲染**: 仅重绘修改的图形元素
- **虚拟滚动**: 大量组件时的性能优化
- **对象池**: 减少对象创建和销毁开销
- **Web Worker**: 复杂计算移至 Worker 线程
- **性能监控**: 实时监控帧率、渲染时间、内存使用

## 📁 项目结构

```
src/
├── components/          # Vue 组件
│   ├── CanvasEditor.vue    # 画布编辑器
│   ├── ComponentLibrary.vue # 组件库
│   ├── PropertiesPanel.vue  # 属性面板
│   ├── Toolbar.vue         # 工具栏
│   ├── EventConfigurator.vue # 事件配置器
│   ├── ProjectManager.vue   # 项目管理
│   └── PerformancePanel.vue # 性能监控
├── stores/              # Pinia 状态管理
│   ├── canvas.ts           # 画布状态
│   ├── components.ts       # 组件状态
│   └── events.ts           # 事件状态
├── utils/               # 工具类
│   ├── meta2d.ts           # Meta2d 封装
│   ├── animation.ts        # GSAP 动画管理
│   ├── keyboard.ts         # 键盘快捷键
│   ├── editor.ts           # 编辑操作
│   ├── grid.ts             # 网格系统
│   ├── storage.ts          # 存储管理
│   └── performance.ts      # 性能优化
└── style.css            # 全局样式
```

## 🎯 开发里程碑

- [x] **阶段 1**: 画布核心系统（尺寸/缩放/背景）
- [x] **阶段 2**: 组件库实现（拖拽添加/分类管理）
- [x] **阶段 3**: 编辑功能（选择/组合/层级）
- [x] **阶段 4**: 事件系统与动画集成
- [x] **阶段 5**: 辅助工具（网格/参考线）
- [x] **阶段 6**: 状态管理完善
- [x] **阶段 7**: 技术整合和性能优化

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 🎮 使用指南

### 基本操作
1. **添加组件**: 从左侧组件库拖拽组件到画布
2. **选择元素**: 点击画布上的元素进行选择
3. **编辑属性**: 在右侧属性面板修改元素属性
4. **配置事件**: 在属性面板的事件配置中添加交互

### 快捷键
- `Ctrl+A` - 全选
- `Ctrl+C` - 复制
- `Ctrl+V` - 粘贴
- `Ctrl+Z` - 撤销
- `Ctrl+Y` - 重做
- `Delete` - 删除

### 项目管理
1. 点击顶部菜单的"项目管理"
2. 创建新项目或加载已有项目
3. 支持项目导入导出功能

## 🔧 配置说明

### 画布配置
- 支持多种预设尺寸
- 自定义背景颜色和图片
- 网格和参考线设置

### 性能配置
- 自动保存间隔设置
- 渲染性能监控
- 内存使用优化

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [Vue.js](https://vuejs.org/) - 渐进式 JavaScript 框架
- [Element Plus](https://element-plus.org/) - Vue 3 组件库
- [GSAP](https://greensock.com/gsap/) - 专业级动画库
- [Pixi.js](https://pixijs.com/) - 2D WebGL 渲染器
- [Meta2d.js](https://meta2d.js.org/) - 2D 图形编辑引擎
