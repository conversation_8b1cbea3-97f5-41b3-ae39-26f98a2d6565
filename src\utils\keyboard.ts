// 键盘快捷键工具类

export interface KeyboardShortcut {
  key: string
  ctrl?: boolean
  shift?: boolean
  alt?: boolean
  callback: () => void
  description: string
}

export class KeyboardManager {
  private shortcuts: KeyboardShortcut[] = []
  private isListening = false

  constructor() {
    this.handleKeyDown = this.handleKeyDown.bind(this)
  }

  // 注册快捷键
  register(shortcut: KeyboardShortcut) {
    this.shortcuts.push(shortcut)
  }

  // 批量注册快捷键
  registerMultiple(shortcuts: KeyboardShortcut[]) {
    this.shortcuts.push(...shortcuts)
  }

  // 开始监听键盘事件
  startListening() {
    if (!this.isListening) {
      document.addEventListener('keydown', this.handleKeyDown)
      this.isListening = true
    }
  }

  // 停止监听键盘事件
  stopListening() {
    if (this.isListening) {
      document.removeEventListener('keydown', this.handleKeyDown)
      this.isListening = false
    }
  }

  // 处理键盘按下事件
  private handleKeyDown(event: KeyboardEvent) {
    // 如果焦点在输入框中，不处理快捷键
    const target = event.target as HTMLElement
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
      return
    }

    const matchedShortcut = this.shortcuts.find(shortcut => {
      return (
        shortcut.key.toLowerCase() === event.key.toLowerCase() &&
        !!shortcut.ctrl === event.ctrlKey &&
        !!shortcut.shift === event.shiftKey &&
        !!shortcut.alt === event.altKey
      )
    })

    if (matchedShortcut) {
      event.preventDefault()
      event.stopPropagation()
      matchedShortcut.callback()
    }
  }

  // 获取所有快捷键
  getShortcuts(): KeyboardShortcut[] {
    return [...this.shortcuts]
  }

  // 清除所有快捷键
  clear() {
    this.shortcuts = []
  }

  // 移除特定快捷键
  remove(key: string, ctrl?: boolean, shift?: boolean, alt?: boolean) {
    this.shortcuts = this.shortcuts.filter(shortcut => 
      !(shortcut.key === key && 
        !!shortcut.ctrl === !!ctrl && 
        !!shortcut.shift === !!shift && 
        !!shortcut.alt === !!alt)
    )
  }
}

// 默认快捷键配置
export const createDefaultShortcuts = (callbacks: {
  selectAll: () => void
  copy: () => void
  cut: () => void
  paste: () => void
  undo: () => void
  redo: () => void
  delete: () => void
  duplicate: () => void
  group: () => void
  ungroup: () => void
  bringToFront: () => void
  sendToBack: () => void
  save: () => void
}): KeyboardShortcut[] => {
  return [
    {
      key: 'a',
      ctrl: true,
      callback: callbacks.selectAll,
      description: '全选'
    },
    {
      key: 'c',
      ctrl: true,
      callback: callbacks.copy,
      description: '复制'
    },
    {
      key: 'x',
      ctrl: true,
      callback: callbacks.cut,
      description: '剪切'
    },
    {
      key: 'v',
      ctrl: true,
      callback: callbacks.paste,
      description: '粘贴'
    },
    {
      key: 'z',
      ctrl: true,
      callback: callbacks.undo,
      description: '撤销'
    },
    {
      key: 'y',
      ctrl: true,
      callback: callbacks.redo,
      description: '重做'
    },
    {
      key: 'z',
      ctrl: true,
      shift: true,
      callback: callbacks.redo,
      description: '重做'
    },
    {
      key: 'Delete',
      callback: callbacks.delete,
      description: '删除'
    },
    {
      key: 'Backspace',
      callback: callbacks.delete,
      description: '删除'
    },
    {
      key: 'd',
      ctrl: true,
      callback: callbacks.duplicate,
      description: '复制元素'
    },
    {
      key: 'g',
      ctrl: true,
      callback: callbacks.group,
      description: '组合'
    },
    {
      key: 'u',
      ctrl: true,
      callback: callbacks.ungroup,
      description: '取消组合'
    },
    {
      key: ']',
      ctrl: true,
      callback: callbacks.bringToFront,
      description: '置顶'
    },
    {
      key: '[',
      ctrl: true,
      callback: callbacks.sendToBack,
      description: '置底'
    },
    {
      key: 's',
      ctrl: true,
      callback: callbacks.save,
      description: '保存'
    }
  ]
}

// 格式化快捷键显示
export const formatShortcut = (shortcut: KeyboardShortcut): string => {
  const parts: string[] = []
  
  if (shortcut.ctrl) parts.push('Ctrl')
  if (shortcut.shift) parts.push('Shift')
  if (shortcut.alt) parts.push('Alt')
  
  parts.push(shortcut.key.toUpperCase())
  
  return parts.join(' + ')
}
