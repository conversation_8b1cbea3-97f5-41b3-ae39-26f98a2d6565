import { defineStore } from 'pinia'
import { ref, reactive, watch } from 'vue'
import { storage, settingsManager } from '@/utils/storage'

export interface CanvasSize {
  width: number
  height: number
}

export interface BackgroundConfig {
  color: string
  image: string | null
  mode: 'original' | 'stretch' | 'fit' | 'repeat' | 'repeat-x' | 'repeat-y' | 'no-repeat' | 'cover' | 'contain'
}

export interface CanvasPosition {
  x: number
  y: number
}

export const useCanvasStore = defineStore('canvas', () => {
  // 画布尺寸预设
  const sizePresets = [
    { name: 'PC - 1920×1080', width: 1920, height: 1080 },
    { name: 'PC - 1600×900', width: 1600, height: 900 },
    { name: 'PC - 1366×768', width: 1366, height: 768 },
    { name: 'PC - 1280×720', width: 1280, height: 720 },
    { name: '移动端 - 1024×1366', width: 1024, height: 1366 },
    { name: '移动端 - 768×1024', width: 768, height: 1024 },
    { name: '移动端 - 480×800', width: 480, height: 800 },
  ]

  // 从存储中加载初始状态
  const loadInitialState = () => {
    return {
      size: storage.get('canvas.size', { width: 1920, height: 1080 }),
      zoom: storage.get('canvas.zoom', 1),
      position: storage.get('canvas.position', { x: 0, y: 0 }),
      background: storage.get('canvas.background', {
        color: '#FFFFFF',
        image: null,
        mode: 'stretch'
      }),
      gridEnabled: storage.get('canvas.gridEnabled', true),
      gridSize: storage.get('canvas.gridSize', 20),
      snapToGrid: storage.get('canvas.snapToGrid', true),
      guidelines: storage.get('canvas.guidelines', { x: [], y: [] }),
      showGuidelines: storage.get('canvas.showGuidelines', true)
    }
  }

  const initialState = loadInitialState()

  // 画布状态
  const size = reactive<CanvasSize>(initialState.size)
  const zoom = ref(initialState.zoom)
  const position = reactive<CanvasPosition>(initialState.position)
  const background = reactive<BackgroundConfig>(initialState.background)

  // 选中的元素
  const selectedElements = ref<any[]>([])

  // 网格设置
  const gridEnabled = ref(initialState.gridEnabled)
  const gridSize = ref(initialState.gridSize)
  const snapToGrid = ref(initialState.snapToGrid)

  // 参考线
  const guidelines = ref<{ x: number[], y: number[] }>(initialState.guidelines)
  const showGuidelines = ref(initialState.showGuidelines)

  // 操作历史
  const history = ref<any[]>([])
  const historyIndex = ref(-1)

  // Actions
  const setCanvasSize = (newSize: CanvasSize) => {
    size.width = newSize.width
    size.height = newSize.height
  }

  const setZoom = (newZoom: number) => {
    zoom.value = Math.max(0.1, Math.min(5, newZoom))
  }

  const setPosition = (newPosition: CanvasPosition) => {
    position.x = newPosition.x
    position.y = newPosition.y
  }

  const setBackgroundColor = (color: string) => {
    background.color = color
  }

  const setBackgroundImage = (imageUrl: string | null) => {
    background.image = imageUrl
  }

  const setBackgroundMode = (mode: BackgroundConfig['mode']) => {
    background.mode = mode
  }

  const selectElements = (elements: any[]) => {
    selectedElements.value = elements
  }

  const addSelectedElement = (element: any) => {
    if (!selectedElements.value.includes(element)) {
      selectedElements.value.push(element)
    }
  }

  const removeSelectedElement = (element: any) => {
    const index = selectedElements.value.indexOf(element)
    if (index > -1) {
      selectedElements.value.splice(index, 1)
    }
  }

  const clearSelection = () => {
    selectedElements.value = []
  }

  const toggleGrid = () => {
    gridEnabled.value = !gridEnabled.value
  }

  const setGridSize = (size: number) => {
    gridSize.value = Math.max(5, Math.min(100, size))
  }

  const toggleSnapToGrid = () => {
    snapToGrid.value = !snapToGrid.value
  }

  const addGuideline = (type: 'x' | 'y', value: number) => {
    if (type === 'x' && !guidelines.value.x.includes(value)) {
      guidelines.value.x.push(value)
    } else if (type === 'y' && !guidelines.value.y.includes(value)) {
      guidelines.value.y.push(value)
    }
  }

  const removeGuideline = (type: 'x' | 'y', value: number) => {
    if (type === 'x') {
      const index = guidelines.value.x.indexOf(value)
      if (index > -1) guidelines.value.x.splice(index, 1)
    } else {
      const index = guidelines.value.y.indexOf(value)
      if (index > -1) guidelines.value.y.splice(index, 1)
    }
  }

  const clearGuidelines = () => {
    guidelines.value.x = []
    guidelines.value.y = []
  }

  const toggleGuidelines = () => {
    showGuidelines.value = !showGuidelines.value
  }

  // 保存状态到存储
  const saveState = () => {
    storage.set('canvas.size', size)
    storage.set('canvas.zoom', zoom.value)
    storage.set('canvas.position', position)
    storage.set('canvas.background', background)
    storage.set('canvas.gridEnabled', gridEnabled.value)
    storage.set('canvas.gridSize', gridSize.value)
    storage.set('canvas.snapToGrid', snapToGrid.value)
    storage.set('canvas.guidelines', guidelines.value)
    storage.set('canvas.showGuidelines', showGuidelines.value)
  }

  // 重置状态
  const resetState = () => {
    size.width = 1920
    size.height = 1080
    zoom.value = 1
    position.x = 0
    position.y = 0
    background.color = '#FFFFFF'
    background.image = null
    background.mode = 'stretch'
    gridEnabled.value = true
    gridSize.value = 20
    snapToGrid.value = true
    guidelines.value = { x: [], y: [] }
    showGuidelines.value = true
    selectedElements.value = []
    history.value = []
    historyIndex.value = -1
  }

  // 导出状态
  const exportState = () => {
    return {
      size: { ...size },
      zoom: zoom.value,
      position: { ...position },
      background: { ...background },
      gridEnabled: gridEnabled.value,
      gridSize: gridSize.value,
      snapToGrid: snapToGrid.value,
      guidelines: { ...guidelines.value },
      showGuidelines: showGuidelines.value
    }
  }

  // 导入状态
  const importState = (state: any) => {
    if (state.size) Object.assign(size, state.size)
    if (state.zoom !== undefined) zoom.value = state.zoom
    if (state.position) Object.assign(position, state.position)
    if (state.background) Object.assign(background, state.background)
    if (state.gridEnabled !== undefined) gridEnabled.value = state.gridEnabled
    if (state.gridSize !== undefined) gridSize.value = state.gridSize
    if (state.snapToGrid !== undefined) snapToGrid.value = state.snapToGrid
    if (state.guidelines) guidelines.value = { ...state.guidelines }
    if (state.showGuidelines !== undefined) showGuidelines.value = state.showGuidelines
  }

  // 监听状态变化并自动保存
  const enableAutoSave = settingsManager.get('autoSave', true)
  if (enableAutoSave) {
    watch([size, zoom, position, background, gridEnabled, gridSize, snapToGrid, guidelines, showGuidelines],
      () => {
        saveState()
      },
      { deep: true }
    )
  }

  return {
    // State
    sizePresets,
    size,
    zoom,
    position,
    background,
    selectedElements,
    gridEnabled,
    gridSize,
    snapToGrid,
    guidelines,
    showGuidelines,
    history,
    historyIndex,
    
    // Actions
    setCanvasSize,
    setZoom,
    setPosition,
    setBackgroundColor,
    setBackgroundImage,
    setBackgroundMode,
    selectElements,
    addSelectedElement,
    removeSelectedElement,
    clearSelection,
    toggleGrid,
    setGridSize,
    toggleSnapToGrid,
    addGuideline,
    removeGuideline,
    clearGuidelines,
    toggleGuidelines,
    saveState,
    resetState,
    exportState,
    importState,
  }
})
