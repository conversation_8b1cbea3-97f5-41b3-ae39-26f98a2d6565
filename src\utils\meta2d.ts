// Meta2d 工具类和类型定义

export interface Meta2dElement {
  id: string
  name: string
  x: number
  y: number
  width: number
  height: number
  rotate?: number
  fillColor?: string
  borderColor?: string
  borderWidth?: number
  opacity?: number
  visible?: boolean
  image?: string
  text?: string
  [key: string]: any
}

export interface Meta2dOptions {
  canvas: HTMLElement
  width: number
  height: number
  color?: string
  activeColor?: string
  hoverColor?: string
  grid?: boolean
  rule?: boolean
  scroll?: boolean
}

export interface Meta2d {
  addPen(pen: Meta2dElement): void
  deletePen(pen: Meta2dElement): void
  updatePen(id: string, props: Partial<Meta2dElement>): void
  resize(width: number, height: number): void
  scale(scale: number): void
  translate(x: number, y: number): void
  copy(): void
  cut(): void
  paste(): void
  undo(): void
  redo(): void
  selectAll(): void
  combine(): void
  uncombine(): void
  bringToFront(): void
  sendToBack(): void
  bringForward(): void
  sendBackward(): void
  on(event: string, callback: Function): void
  off(event: string, callback?: Function): void
  emit(event: string, ...args: any[]): void
  destroy(): void
  render(): void
  getSelectedElements(): Meta2dElement[]
  getElementById(id: string): Meta2dElement | null
  getAllElements(): Meta2dElement[]
}

// 模拟Meta2d实现（用于开发测试）
class MockMeta2d implements Meta2d {
  private elements: Meta2dElement[] = []
  private selectedElements: Meta2dElement[] = []
  private eventListeners: Record<string, Function[]> = {}
  private canvas: HTMLElement
  private options: Meta2dOptions

  constructor(options: Meta2dOptions) {
    this.canvas = options.canvas
    this.options = options
    this.initCanvas()
  }

  private initCanvas() {
    // 创建canvas元素
    const canvas = document.createElement('canvas')
    canvas.width = this.options.width
    canvas.height = this.options.height
    canvas.style.width = '100%'
    canvas.style.height = '100%'
    canvas.style.border = '1px solid #ddd'
    canvas.style.background = this.options.color || '#ffffff'
    
    this.canvas.appendChild(canvas)
    
    // 添加事件监听
    canvas.addEventListener('click', this.handleCanvasClick.bind(this))
    canvas.addEventListener('mousemove', this.handleCanvasMouseMove.bind(this))
  }

  private handleCanvasClick(event: MouseEvent) {
    const rect = (event.target as HTMLCanvasElement).getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top
    
    // 简单的点击检测
    const clickedElement = this.elements.find(el => 
      x >= el.x && x <= el.x + el.width &&
      y >= el.y && y <= el.y + el.height
    )
    
    if (clickedElement) {
      this.selectedElements = [clickedElement]
      this.emit('active', this.selectedElements)
    } else {
      this.selectedElements = []
      this.emit('inactive')
    }
    
    this.render()
  }

  private handleCanvasMouseMove(event: MouseEvent) {
    // 处理鼠标移动事件
  }

  addPen(pen: Meta2dElement): void {
    this.elements.push(pen)
    this.emit('add', pen)
    this.render()
  }

  deletePen(pen: Meta2dElement): void {
    const index = this.elements.findIndex(el => el.id === pen.id)
    if (index > -1) {
      this.elements.splice(index, 1)
      this.emit('delete', pen)
      this.render()
    }
  }

  updatePen(id: string, props: Partial<Meta2dElement>): void {
    const element = this.elements.find(el => el.id === id)
    if (element) {
      Object.assign(element, props)
      this.emit('update', element)
      this.render()
    }
  }

  resize(width: number, height: number): void {
    this.options.width = width
    this.options.height = height
    const canvas = this.canvas.querySelector('canvas')
    if (canvas) {
      canvas.width = width
      canvas.height = height
    }
    this.render()
  }

  scale(scale: number): void {
    const canvas = this.canvas.querySelector('canvas')
    if (canvas) {
      canvas.style.transform = `scale(${scale})`
    }
  }

  translate(x: number, y: number): void {
    const canvas = this.canvas.querySelector('canvas')
    if (canvas) {
      canvas.style.transform = `translate(${x}px, ${y}px)`
    }
  }

  copy(): void {
    console.log('Copy operation')
  }

  cut(): void {
    console.log('Cut operation')
  }

  paste(): void {
    console.log('Paste operation')
  }

  undo(): void {
    console.log('Undo operation')
  }

  redo(): void {
    console.log('Redo operation')
  }

  selectAll(): void {
    this.selectedElements = [...this.elements]
    this.emit('active', this.selectedElements)
    this.render()
  }

  combine(): void {
    console.log('Combine operation')
  }

  uncombine(): void {
    console.log('Uncombine operation')
  }

  bringToFront(): void {
    console.log('Bring to front operation')
  }

  sendToBack(): void {
    console.log('Send to back operation')
  }

  bringForward(): void {
    console.log('Bring forward operation')
  }

  sendBackward(): void {
    console.log('Send backward operation')
  }

  on(event: string, callback: Function): void {
    if (!this.eventListeners[event]) {
      this.eventListeners[event] = []
    }
    this.eventListeners[event].push(callback)
  }

  off(event: string, callback?: Function): void {
    if (callback) {
      const listeners = this.eventListeners[event] || []
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    } else {
      delete this.eventListeners[event]
    }
  }

  emit(event: string, ...args: any[]): void {
    const listeners = this.eventListeners[event] || []
    listeners.forEach(callback => callback(...args))
  }

  destroy(): void {
    this.canvas.innerHTML = ''
    this.eventListeners = {}
    this.elements = []
    this.selectedElements = []
  }

  render(): void {
    const canvas = this.canvas.querySelector('canvas') as HTMLCanvasElement
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // 绘制所有元素
    this.elements.forEach(element => {
      this.drawElement(ctx, element)
    })

    // 绘制选中状态
    this.selectedElements.forEach(element => {
      this.drawSelection(ctx, element)
    })
  }

  private drawElement(ctx: CanvasRenderingContext2D, element: Meta2dElement): void {
    ctx.save()
    
    // 设置样式
    ctx.fillStyle = element.fillColor || '#409eff'
    ctx.strokeStyle = element.borderColor || '#409eff'
    ctx.lineWidth = element.borderWidth || 1
    ctx.globalAlpha = element.opacity !== undefined ? element.opacity : 1

    // 绘制不同类型的元素
    switch (element.name) {
      case 'rectangle':
        ctx.fillRect(element.x, element.y, element.width, element.height)
        ctx.strokeRect(element.x, element.y, element.width, element.height)
        break
      case 'circle':
        const centerX = element.x + element.width / 2
        const centerY = element.y + element.height / 2
        const radius = Math.min(element.width, element.height) / 2
        ctx.beginPath()
        ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI)
        ctx.fill()
        ctx.stroke()
        break
      case 'triangle':
        ctx.beginPath()
        ctx.moveTo(element.x + element.width / 2, element.y)
        ctx.lineTo(element.x, element.y + element.height)
        ctx.lineTo(element.x + element.width, element.y + element.height)
        ctx.closePath()
        ctx.fill()
        ctx.stroke()
        break
      case 'image':
        if (element.image) {
          const img = new Image()
          img.onload = () => {
            ctx.drawImage(img, element.x, element.y, element.width, element.height)
          }
          img.src = element.image
        }
        break
    }
    
    ctx.restore()
  }

  private drawSelection(ctx: CanvasRenderingContext2D, element: Meta2dElement): void {
    ctx.save()
    ctx.strokeStyle = '#409eff'
    ctx.lineWidth = 2
    ctx.setLineDash([5, 5])
    ctx.strokeRect(element.x - 2, element.y - 2, element.width + 4, element.height + 4)
    ctx.restore()
  }

  getSelectedElements(): Meta2dElement[] {
    return [...this.selectedElements]
  }

  getElementById(id: string): Meta2dElement | null {
    return this.elements.find(el => el.id === id) || null
  }

  getAllElements(): Meta2dElement[] {
    return [...this.elements]
  }
}

// 创建Meta2d实例的工厂函数
export const createMeta2d = (options: Meta2dOptions): Meta2d => {
  // 在实际项目中，这里应该使用真正的Meta2d
  // return create(options)
  
  // 目前使用模拟实现
  return new MockMeta2d(options)
}
