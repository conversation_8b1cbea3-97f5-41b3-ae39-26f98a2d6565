<template>
  <el-dialog
    v-model="visible"
    title="项目管理"
    width="800px"
    :before-close="handleClose"
  >
    <div class="project-manager">
      <div class="manager-header">
        <el-button type="primary" @click="showNewProjectDialog = true">
          <el-icon><Plus /></el-icon>
          新建项目
        </el-button>
        
        <el-button @click="importProject">
          <el-icon><Upload /></el-icon>
          导入项目
        </el-button>
        
        <el-button @click="showStorageInfo = !showStorageInfo">
          <el-icon><DataAnalysis /></el-icon>
          存储信息
        </el-button>
      </div>

      <!-- 存储信息 -->
      <el-collapse-transition>
        <div v-show="showStorageInfo" class="storage-info">
          <el-descriptions title="存储使用情况" :column="3" border>
            <el-descriptions-item label="项目数量">
              {{ storageInfo.projectCount }}
            </el-descriptions-item>
            <el-descriptions-item label="存储大小">
              {{ storageInfo.formattedSize }}
            </el-descriptions-item>
            <el-descriptions-item label="性能等级">
              <el-tag :type="getPerformanceTagType()">
                {{ getPerformanceLevelText() }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-collapse-transition>

      <!-- 项目列表 -->
      <div class="project-list">
        <el-table :data="projectList" style="width: 100%">
          <el-table-column prop="name" label="项目名称" />
          <el-table-column prop="version" label="版本" width="80" />
          <el-table-column prop="createdAt" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.createdAt) }}
            </template>
          </el-table-column>
          <el-table-column prop="updatedAt" label="更新时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.updatedAt) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <el-button size="small" @click="loadProject(row.name)">
                加载
              </el-button>
              <el-button size="small" @click="exportProject(row.name)">
                导出
              </el-button>
              <el-button 
                size="small" 
                type="danger" 
                @click="deleteProject(row.name)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 新建项目对话框 -->
    <el-dialog
      v-model="showNewProjectDialog"
      title="新建项目"
      width="400px"
      append-to-body
    >
      <el-form :model="newProjectForm" label-width="80px">
        <el-form-item label="项目名称">
          <el-input v-model="newProjectForm.name" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="画布尺寸">
          <el-select v-model="newProjectForm.preset" style="width: 100%">
            <el-option
              v-for="preset in canvasStore.sizePresets"
              :key="preset.name"
              :label="preset.name"
              :value="preset.name"
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showNewProjectDialog = false">取消</el-button>
        <el-button type="primary" @click="createProject">创建</el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Upload, DataAnalysis } from '@element-plus/icons-vue'
import { projectManager, type ProjectState } from '@/utils/storage'
import { performanceMonitor } from '@/utils/performance'
import { useCanvasStore } from '@/stores/canvas'
import { useComponentStore } from '@/stores/components'
import { useEventStore } from '@/stores/events'

const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const canvasStore = useCanvasStore()
const componentStore = useComponentStore()
const eventStore = useEventStore()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const showNewProjectDialog = ref(false)
const showStorageInfo = ref(false)
const projectList = ref<any[]>([])
const storageInfo = ref({ projectCount: 0, formattedSize: '0 Bytes' })

const newProjectForm = ref({
  name: '',
  preset: 'PC - 1920×1080'
})

// 加载项目列表
const loadProjectList = () => {
  projectList.value = projectManager.getProjectList()
  storageInfo.value = projectManager.getStorageInfo()
}

// 创建新项目
const createProject = () => {
  if (!newProjectForm.value.name.trim()) {
    ElMessage.error('请输入项目名称')
    return
  }

  // 检查项目名称是否已存在
  const exists = projectList.value.some(p => p.name === newProjectForm.value.name)
  if (exists) {
    ElMessage.error('项目名称已存在')
    return
  }

  // 设置画布尺寸
  const preset = canvasStore.sizePresets.find(p => p.name === newProjectForm.value.preset)
  if (preset) {
    canvasStore.setCanvasSize({ width: preset.width, height: preset.height })
  }

  // 重置状态
  canvasStore.resetState()

  // 保存项目
  const state: ProjectState = {
    canvas: canvasStore.exportState(),
    elements: [], // TODO: 从Meta2d获取元素
    events: eventStore.exportEvents(),
    history: [],
    settings: {
      autoSave: true,
      autoSaveInterval: 30000,
      theme: 'light',
      language: 'zh-CN'
    }
  }

  if (projectManager.saveProject(newProjectForm.value.name, state)) {
    ElMessage.success('项目创建成功')
    showNewProjectDialog.value = false
    newProjectForm.value.name = ''
    loadProjectList()
  } else {
    ElMessage.error('项目创建失败')
  }
}

// 加载项目
const loadProject = (name: string) => {
  const state = projectManager.loadProject(name)
  if (state) {
    // 导入状态
    canvasStore.importState(state.canvas)
    eventStore.importEvents(state.events)
    
    ElMessage.success(`项目 "${name}" 加载成功`)
    visible.value = false
  } else {
    ElMessage.error('项目加载失败')
  }
}

// 导出项目
const exportProject = (name: string) => {
  const data = projectManager.exportProject(name)
  if (data) {
    // 创建下载链接
    const blob = new Blob([data], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${name}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    ElMessage.success('项目导出成功')
  } else {
    ElMessage.error('项目导出失败')
  }
}

// 导入项目
const importProject = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'
  input.onchange = (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const data = e.target?.result as string
        if (projectManager.importProject(data)) {
          ElMessage.success('项目导入成功')
          loadProjectList()
        } else {
          ElMessage.error('项目导入失败')
        }
      }
      reader.readAsText(file)
    }
  }
  input.click()
}

// 删除项目
const deleteProject = (name: string) => {
  ElMessageBox.confirm(
    `确定要删除项目 "${name}" 吗？此操作不可恢复。`,
    '确认删除',
    {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    if (projectManager.deleteProject(name)) {
      ElMessage.success('项目删除成功')
      loadProjectList()
    } else {
      ElMessage.error('项目删除失败')
    }
  }).catch(() => {
    // 用户取消
  })
}

// 格式化日期
const formatDate = (timestamp: number) => {
  return new Date(timestamp).toLocaleString()
}

// 获取性能等级文本
const getPerformanceLevelText = () => {
  const level = performanceMonitor.getPerformanceLevel()
  const texts = {
    excellent: '优秀',
    good: '良好',
    fair: '一般',
    poor: '较差'
  }
  return texts[level]
}

// 获取性能标签类型
const getPerformanceTagType = () => {
  const level = performanceMonitor.getPerformanceLevel()
  const types = {
    excellent: 'success',
    good: 'success',
    fair: 'warning',
    poor: 'danger'
  }
  return types[level] as any
}

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 组件挂载时加载项目列表
onMounted(() => {
  loadProjectList()
})
</script>

<style scoped>
.project-manager {
  padding: 20px 0;
}

.manager-header {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.storage-info {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.project-list {
  margin-top: 20px;
}

:deep(.el-descriptions__title) {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
}
</style>
