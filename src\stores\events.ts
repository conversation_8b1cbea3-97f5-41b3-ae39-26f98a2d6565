// 事件系统状态管理
import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import { animationManager } from '@/utils/animation'
import type { Meta2dElement } from '@/utils/meta2d'

export interface EventAction {
  id: string
  type: 'changeProp' | 'playAnimation' | 'navigate' | 'toggleVisible' | 'sendMessage' | 'executeScript'
  params: Record<string, any>
  delay?: number
}

export interface ElementEvent {
  id: string
  elementId: string
  type: 'click' | 'mouseover' | 'mouseout' | 'dblclick' | 'contextmenu' | 'mousedown' | 'mouseup'
  actions: EventAction[]
  enabled: boolean
}

export interface GlobalEvent {
  id: string
  type: 'pageLoad' | 'canvasResize' | 'elementAdd' | 'elementDelete' | 'selectionChange'
  actions: EventAction[]
  enabled: boolean
}

export const useEventStore = defineStore('events', () => {
  // 元素事件
  const elementEvents = ref<ElementEvent[]>([])
  
  // 全局事件
  const globalEvents = ref<GlobalEvent[]>([])
  
  // 事件执行历史
  const eventHistory = ref<{
    timestamp: number
    eventId: string
    actionId: string
    success: boolean
    error?: string
  }[]>([])

  // 添加元素事件
  const addElementEvent = (elementId: string, eventType: ElementEvent['type']): ElementEvent => {
    const event: ElementEvent = {
      id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      elementId,
      type: eventType,
      actions: [],
      enabled: true
    }
    
    elementEvents.value.push(event)
    return event
  }

  // 删除元素事件
  const removeElementEvent = (eventId: string) => {
    const index = elementEvents.value.findIndex(e => e.id === eventId)
    if (index > -1) {
      elementEvents.value.splice(index, 1)
    }
  }

  // 获取元素的所有事件
  const getElementEvents = (elementId: string): ElementEvent[] => {
    return elementEvents.value.filter(e => e.elementId === elementId)
  }

  // 添加事件动作
  const addEventAction = (eventId: string, actionType: EventAction['type']): EventAction => {
    const event = elementEvents.value.find(e => e.id === eventId) || 
                  globalEvents.value.find(e => e.id === eventId)
    
    if (!event) {
      throw new Error(`Event with id ${eventId} not found`)
    }

    const action: EventAction = {
      id: `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: actionType,
      params: {},
      delay: 0
    }

    event.actions.push(action)
    return action
  }

  // 删除事件动作
  const removeEventAction = (eventId: string, actionId: string) => {
    const event = elementEvents.value.find(e => e.id === eventId) || 
                  globalEvents.value.find(e => e.id === eventId)
    
    if (event) {
      const index = event.actions.findIndex(a => a.id === actionId)
      if (index > -1) {
        event.actions.splice(index, 1)
      }
    }
  }

  // 更新事件动作参数
  const updateActionParams = (eventId: string, actionId: string, params: Record<string, any>) => {
    const event = elementEvents.value.find(e => e.id === eventId) || 
                  globalEvents.value.find(e => e.id === eventId)
    
    if (event) {
      const action = event.actions.find(a => a.id === actionId)
      if (action) {
        action.params = { ...action.params, ...params }
      }
    }
  }

  // 执行事件动作
  const executeAction = async (action: EventAction, element?: Meta2dElement, meta2d?: any): Promise<boolean> => {
    try {
      // 延迟执行
      if (action.delay && action.delay > 0) {
        await new Promise(resolve => setTimeout(resolve, action.delay! * 1000))
      }

      switch (action.type) {
        case 'changeProp':
          if (element && meta2d) {
            const { property, value, duration } = action.params
            if (duration && duration > 0) {
              // 使用动画改变属性
              animationManager.animateElement(element, { [property]: value }, {
                duration,
                onUpdate: () => meta2d.render()
              })
            } else {
              // 直接改变属性
              meta2d.updatePen(element.id, { [property]: value })
            }
          }
          break

        case 'playAnimation':
          if (element) {
            const { animationType, duration, delay, easing } = action.params
            animationManager.playPreset(element, animationType, {
              duration,
              delay,
              ease: easing,
              onUpdate: () => meta2d?.render()
            })
          }
          break

        case 'navigate':
          const { url, target } = action.params
          if (url) {
            window.open(url, target || '_self')
          }
          break

        case 'toggleVisible':
          if (meta2d) {
            const { targetId, operation } = action.params
            const targetElement = meta2d.getElementById(targetId)
            if (targetElement) {
              let visible = targetElement.visible
              switch (operation) {
                case 'show':
                  visible = true
                  break
                case 'hide':
                  visible = false
                  break
                case 'toggle':
                  visible = !visible
                  break
              }
              meta2d.updatePen(targetId, { visible })
            }
          }
          break

        case 'sendMessage':
          const { messageType, message } = action.params
          // 发送自定义消息事件
          window.dispatchEvent(new CustomEvent('canvas-message', {
            detail: { type: messageType, message }
          }))
          break

        case 'executeScript':
          const { script } = action.params
          if (script) {
            // 在安全的上下文中执行脚本
            const func = new Function('element', 'meta2d', 'animationManager', script)
            func(element, meta2d, animationManager)
          }
          break
      }

      // 记录成功执行
      eventHistory.value.push({
        timestamp: Date.now(),
        eventId: '',
        actionId: action.id,
        success: true
      })

      return true
    } catch (error) {
      console.error('Error executing action:', error)
      
      // 记录执行失败
      eventHistory.value.push({
        timestamp: Date.now(),
        eventId: '',
        actionId: action.id,
        success: false,
        error: error instanceof Error ? error.message : String(error)
      })

      return false
    }
  }

  // 执行元素事件
  const executeElementEvent = async (elementId: string, eventType: ElementEvent['type'], element?: Meta2dElement, meta2d?: any) => {
    const events = elementEvents.value.filter(e => 
      e.elementId === elementId && 
      e.type === eventType && 
      e.enabled
    )

    for (const event of events) {
      for (const action of event.actions) {
        await executeAction(action, element, meta2d)
      }
    }
  }

  // 执行全局事件
  const executeGlobalEvent = async (eventType: GlobalEvent['type'], data?: any, meta2d?: any) => {
    const events = globalEvents.value.filter(e => 
      e.type === eventType && 
      e.enabled
    )

    for (const event of events) {
      for (const action of event.actions) {
        await executeAction(action, undefined, meta2d)
      }
    }
  }

  // 绑定元素事件监听器
  const bindElementEvents = (element: Meta2dElement, meta2d: any) => {
    const events = getElementEvents(element.id)
    
    events.forEach(event => {
      const handler = () => {
        executeElementEvent(element.id, event.type, element, meta2d)
      }

      // 这里应该根据实际的Meta2d API来绑定事件
      // meta2d.on(`${element.id}:${event.type}`, handler)
      console.log(`Binding ${event.type} event for element ${element.id}`)
    })
  }

  // 清理事件历史
  const clearEventHistory = () => {
    eventHistory.value = []
  }

  // 获取事件统计
  const getEventStats = () => {
    const total = eventHistory.value.length
    const successful = eventHistory.value.filter(h => h.success).length
    const failed = total - successful
    
    return { total, successful, failed }
  }

  // 导出事件配置
  const exportEvents = () => {
    return {
      elementEvents: elementEvents.value,
      globalEvents: globalEvents.value
    }
  }

  // 导入事件配置
  const importEvents = (data: { elementEvents: ElementEvent[], globalEvents: GlobalEvent[] }) => {
    elementEvents.value = data.elementEvents || []
    globalEvents.value = data.globalEvents || []
  }

  return {
    // State
    elementEvents,
    globalEvents,
    eventHistory,
    
    // Actions
    addElementEvent,
    removeElementEvent,
    getElementEvents,
    addEventAction,
    removeEventAction,
    updateActionParams,
    executeAction,
    executeElementEvent,
    executeGlobalEvent,
    bindElementEvents,
    clearEventHistory,
    getEventStats,
    exportEvents,
    importEvents
  }
})
