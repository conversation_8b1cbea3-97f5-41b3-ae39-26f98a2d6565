<template>
  <div class="event-configurator">
    <div class="event-list">
      <div
        v-for="(event, index) in elementEvents"
        :key="index"
        class="event-item"
      >
        <div class="event-header">
          <el-select
            v-model="event.type"
            placeholder="选择事件"
            size="small"
            style="flex: 1"
            @change="updateEvent(index)"
          >
            <el-option
              v-for="eventType in eventTypes"
              :key="eventType.value"
              :label="eventType.label"
              :value="eventType.value"
            />
          </el-select>
          
          <el-button
            size="small"
            type="danger"
            text
            @click="removeEvent(index)"
          >
            <el-icon><Delete /></el-icon>
          </el-button>
        </div>
        
        <div v-if="event.type" class="event-actions">
          <div
            v-for="(action, actionIndex) in event.actions"
            :key="actionIndex"
            class="action-item"
          >
            <el-select
              v-model="action.type"
              placeholder="选择动作"
              size="small"
              style="flex: 1"
              @change="updateAction(index, actionIndex)"
            >
              <el-option
                v-for="actionType in actionTypes"
                :key="actionType.value"
                :label="actionType.label"
                :value="actionType.value"
              />
            </el-select>
            
            <el-button
              size="small"
              type="danger"
              text
              @click="removeAction(index, actionIndex)"
            >
              <el-icon><Minus /></el-icon>
            </el-button>
          </div>
          
          <!-- 动作参数配置 -->
          <div
            v-for="(action, actionIndex) in event.actions"
            :key="`params-${actionIndex}`"
            class="action-params"
          >
            <ActionParams
              v-if="action.type"
              :action="action"
              @update="updateActionParams(index, actionIndex, $event)"
            />
          </div>
          
          <el-button
            size="small"
            type="primary"
            text
            @click="addAction(index)"
          >
            <el-icon><Plus /></el-icon>
            添加动作
          </el-button>
        </div>
      </div>
    </div>
    
    <el-button
      size="small"
      type="primary"
      @click="addEvent"
      style="width: 100%; margin-top: 12px"
    >
      <el-icon><Plus /></el-icon>
      添加事件
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { Delete, Minus, Plus } from '@element-plus/icons-vue'
import ActionParams from './ActionParams.vue'
import { useEventStore } from '@/stores/events'
import type { ElementEvent, EventAction } from '@/stores/events'

const props = defineProps<{
  element: any
}>()

const eventStore = useEventStore()

// 事件类型
const eventTypes = [
  { label: '点击', value: 'click' },
  { label: '鼠标悬停', value: 'mouseover' },
  { label: '鼠标离开', value: 'mouseout' },
  { label: '双击', value: 'dblclick' },
  { label: '右键点击', value: 'contextmenu' },
  { label: '鼠标按下', value: 'mousedown' },
  { label: '鼠标释放', value: 'mouseup' }
]

// 动作类型
const actionTypes = [
  { label: '改变属性', value: 'changeProp' },
  { label: '播放动画', value: 'playAnimation' },
  { label: '跳转页面', value: 'navigate' },
  { label: '显示/隐藏', value: 'toggleVisible' },
  { label: '发送消息', value: 'sendMessage' },
  { label: '执行脚本', value: 'executeScript' }
]

// 获取当前元素的事件
const elementEvents = computed(() => {
  if (!props.element) return []
  return eventStore.getElementEvents(props.element.id)
})

// 添加事件
const addEvent = () => {
  elementEvents.push({
    type: '',
    actions: []
  })
}

// 移除事件
const removeEvent = (index: number) => {
  elementEvents.splice(index, 1)
  saveEvents()
}

// 更新事件
const updateEvent = (index: number) => {
  saveEvents()
}

// 添加动作
const addAction = (eventIndex: number) => {
  elementEvents[eventIndex].actions.push({
    type: '',
    params: {}
  })
}

// 移除动作
const removeAction = (eventIndex: number, actionIndex: number) => {
  elementEvents[eventIndex].actions.splice(actionIndex, 1)
  saveEvents()
}

// 更新动作
const updateAction = (eventIndex: number, actionIndex: number) => {
  saveEvents()
}

// 更新动作参数
const updateActionParams = (eventIndex: number, actionIndex: number, params: Record<string, any>) => {
  elementEvents[eventIndex].actions[actionIndex].params = params
  saveEvents()
}

// 保存事件配置
const saveEvents = () => {
  if (props.element) {
    // 这里应该调用Meta2d的API来保存事件配置
    // meta2d.updatePen(props.element.id, { events: elementEvents })
    console.log('Save events for element:', props.element.id, elementEvents)
  }
}
</script>

<style scoped>
.event-configurator {
  width: 100%;
}

.event-list {
  margin-bottom: 12px;
}

.event-item {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
  background: #fafafa;
}

.event-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.event-actions {
  padding-left: 12px;
  border-left: 2px solid #e4e7ed;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.action-params {
  margin-bottom: 12px;
  padding: 8px;
  background: white;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}
</style>
