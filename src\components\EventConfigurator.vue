<template>
  <div class="event-configurator">
    <div class="event-list">
      <div
        v-for="event in elementEvents"
        :key="event.id"
        class="event-item"
      >
        <div class="event-header">
          <el-select
            :model-value="event.type"
            placeholder="选择事件"
            size="small"
            style="flex: 1"
            @change="updateEventType(event.id, $event)"
          >
            <el-option
              v-for="eventType in eventTypes"
              :key="eventType.value"
              :label="eventType.label"
              :value="eventType.value"
            />
          </el-select>

          <el-button
            size="small"
            type="danger"
            text
            @click="removeEvent(event.id)"
          >
            <el-icon><Delete /></el-icon>
          </el-button>
        </div>
        
        <div v-if="event.type" class="event-actions">
          <div
            v-for="action in event.actions"
            :key="action.id"
            class="action-item"
          >
            <el-select
              :model-value="action.type"
              placeholder="选择动作"
              size="small"
              style="flex: 1"
              @change="updateActionType(event.id, action.id, $event)"
            >
              <el-option
                v-for="actionType in actionTypes"
                :key="actionType.value"
                :label="actionType.label"
                :value="actionType.value"
              />
            </el-select>

            <el-button
              size="small"
              type="danger"
              text
              @click="removeAction(event.id, action.id)"
            >
              <el-icon><Minus /></el-icon>
            </el-button>
          </div>

          <!-- 动作参数配置 -->
          <div
            v-for="action in event.actions"
            :key="`params-${action.id}`"
            class="action-params"
          >
            <ActionParams
              v-if="action.type"
              :action="action"
              @update="updateActionParams(event.id, action.id, $event)"
            />
          </div>

          <el-button
            size="small"
            type="primary"
            text
            @click="addAction(event.id)"
          >
            <el-icon><Plus /></el-icon>
            添加动作
          </el-button>
        </div>
      </div>
    </div>
    
    <el-button
      size="small"
      type="primary"
      @click="addEvent"
      style="width: 100%; margin-top: 12px"
    >
      <el-icon><Plus /></el-icon>
      添加事件
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { Delete, Minus, Plus } from '@element-plus/icons-vue'
import ActionParams from './ActionParams.vue'
import { useEventStore } from '@/stores/events'
import type { ElementEvent, EventAction } from '@/stores/events'

const props = defineProps<{
  element: any
}>()

const eventStore = useEventStore()

// 事件类型
const eventTypes = [
  { label: '点击', value: 'click' },
  { label: '鼠标悬停', value: 'mouseover' },
  { label: '鼠标离开', value: 'mouseout' },
  { label: '双击', value: 'dblclick' },
  { label: '右键点击', value: 'contextmenu' },
  { label: '鼠标按下', value: 'mousedown' },
  { label: '鼠标释放', value: 'mouseup' }
]

// 动作类型
const actionTypes = [
  { label: '改变属性', value: 'changeProp' },
  { label: '播放动画', value: 'playAnimation' },
  { label: '跳转页面', value: 'navigate' },
  { label: '显示/隐藏', value: 'toggleVisible' },
  { label: '发送消息', value: 'sendMessage' },
  { label: '执行脚本', value: 'executeScript' }
]

// 获取当前元素的事件
const elementEvents = computed(() => {
  if (!props.element) return []
  return eventStore.getElementEvents(props.element.id)
})

// 添加事件
const addEvent = () => {
  if (!props.element) return
  eventStore.addElementEvent(props.element.id, 'click')
}

// 移除事件
const removeEvent = (eventId: string) => {
  eventStore.removeElementEvent(eventId)
}

// 更新事件类型
const updateEventType = (eventId: string, newType: any) => {
  // 由于事件类型是不可变的，需要删除旧事件并创建新事件
  const event = elementEvents.value.find(e => e.id === eventId)
  if (event && props.element) {
    // 保存动作
    const actions = [...event.actions]
    // 删除旧事件
    eventStore.removeElementEvent(eventId)
    // 创建新事件
    const newEvent = eventStore.addElementEvent(props.element.id, newType)
    // 恢复动作
    actions.forEach(action => {
      const newAction = eventStore.addEventAction(newEvent.id, action.type)
      eventStore.updateActionParams(newEvent.id, newAction.id, action.params)
    })
  }
}

// 添加动作
const addAction = (eventId: string) => {
  eventStore.addEventAction(eventId, 'changeProp')
}

// 移除动作
const removeAction = (eventId: string, actionId: string) => {
  eventStore.removeEventAction(eventId, actionId)
}

// 更新动作类型
const updateActionType = (eventId: string, actionId: string, newType: any) => {
  const event = elementEvents.value.find(e => e.id === eventId)
  if (event) {
    const action = event.actions.find(a => a.id === actionId)
    if (action) {
      // 保存参数
      const params = { ...action.params }
      // 删除旧动作
      eventStore.removeEventAction(eventId, actionId)
      // 创建新动作
      const newAction = eventStore.addEventAction(eventId, newType)
      // 恢复参数（如果适用）
      eventStore.updateActionParams(eventId, newAction.id, params)
    }
  }
}

// 更新动作参数
const updateActionParams = (eventId: string, actionId: string, params: Record<string, any>) => {
  eventStore.updateActionParams(eventId, actionId, params)
}
</script>

<style scoped>
.event-configurator {
  width: 100%;
}

.event-list {
  margin-bottom: 12px;
}

.event-item {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
  background: #fafafa;
}

.event-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.event-actions {
  padding-left: 12px;
  border-left: 2px solid #e4e7ed;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.action-params {
  margin-bottom: 12px;
  padding: 8px;
  background: white;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}
</style>
